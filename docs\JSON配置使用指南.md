# JSON配置使用指南 📋

## 🎯 概述

本工具使用JSON格式的配置文件来管理字段映射关系，提供了灵活、高效的配置管理方式。

## 📁 配置文件位置

```
📂 项目文件夹
└── 📂 data
    └── 📂 config
        └── 📄 field_mappings.json  ← 主配置文件
        └── 📄 配置说明.md         ← 配置说明文档
```

## 🏗️ 配置文件结构

### 基本结构
```json
{
  "description": "配置文件描述",
  "version": "版本号",
  "last_updated": "最后更新时间",
  "marketplaces": {
    "商城类型": {
      "name": "商城名称",
      "description": "商城描述",
      "field_mappings": {},
      "default_values": {}
    }
  },
  "data_sources": {},
  "sku_generation_rules": {},
  "item_name_generation_rules": {}
}
```

### 商城配置结构
```json
"low_price": {
  "name": "低价商城",
  "description": "低价商城字段映射配置",
  "field_mappings": {
    "partial_update": {
      "字段名": {
        "source": "数据来源",
        "value": "固定值",
        "parent": "父体值",
        "child": "子体值",
        "description": "字段说明"
      }
    }
  },
  "default_values": {
    "parent": {
      "字段名": "默认值"
    },
    "child": {
      "字段名": "默认值"
    }
  }
}
```

## 🔧 字段映射配置

### 数据来源映射
从目标文件的指定列获取数据：
```json
"product_description": {
  "source": "target_listing.品名",
  "description": "产品描述 - 使用品名字段"
}
```

### 固定值配置
设置固定的字段值：
```json
"brand_name": {
  "value": "GENERIC",
  "description": "品牌 - 固定值GENERIC"
}
```

### 父子体差异配置
为父体和子体设置不同的值：
```json
"record_action": {
  "parent": "",
  "child": "Partial Update",
  "description": "记录操作 - 父体为空，子体为部分更新"
}
```

### 动态生成配置
使用程序逻辑生成值：
```json
"seller_sku": {
  "parent": "generated",
  "child": "target_listing.MSKU",
  "description": "卖家SKU - 父体动态生成，子体直接映射MSKU"
}
```

## 🛠️ 配置管理工具

### 查看配置摘要
```bash
python scripts/config_manager.py summary
```

### 查看特定商城配置
```bash
# 查看低价商城配置
python scripts/config_manager.py marketplace low_price

# 查看FBA商城配置
python scripts/config_manager.py marketplace fba
```

### 查看字段映射详情
```bash
# 查看部分更新映射
python scripts/config_manager.py mappings low_price partial_update

# 查看完全更新映射
python scripts/config_manager.py mappings fba full_update
```

### 验证配置文件
```bash
python scripts/config_manager.py validate
```

### 查看数据源配置
```bash
python scripts/config_manager.py sources
```

### 查看生成规则
```bash
python scripts/config_manager.py rules
```

## 📝 常见配置修改示例

### 修改品牌名称
```json
"brand_name": {
  "value": "YOUR_BRAND_NAME",  // 改成你的品牌名
  "description": "品牌名称"
}
```

### 修改原产国
```json
"country_of_origin": {
  "value": "USA",  // 从China改为USA
  "description": "原产国"
}
```

### 添加新的数据源映射
```json
"new_field": {
  "source": "target_listing.新列名",
  "description": "新字段说明"
}
```

### 设置条件性填充
```json
"special_field": {
  "parent": "Parent Value",
  "child": "Child Value",
  "description": "父子体不同值的字段"
}
```

## ⚠️ 注意事项

### JSON语法要求
1. **字符串必须用双引号** - 不能使用单引号
2. **最后一个元素后不能有逗号** - 避免尾随逗号
3. **大括号和方括号必须配对** - 检查括号匹配
4. **特殊字符需要转义** - 如 `\"`, `\\`, `\n` 等

### 配置修改流程
1. **备份原文件** - 修改前先备份
2. **使用文本编辑器** - 推荐VS Code、Notepad++等
3. **验证语法** - 使用JSON验证工具
4. **测试配置** - 运行验证脚本
5. **重启程序** - 修改后重启程序生效

### 常见错误
1. **语法错误** - 缺少引号、逗号等
2. **字段名错误** - 字段名与模板不匹配
3. **数据源错误** - 引用不存在的列名
4. **编码问题** - 使用UTF-8编码保存

## 🔍 故障排除

### 配置文件损坏
```bash
# 从备份恢复
cp data/config/field_mappings.json.bak data/config/field_mappings.json
```

### 语法错误检查
使用在线JSON验证工具或：
```bash
python -m json.tool data/config/field_mappings.json
```

### 查看程序日志
```bash
# 查看最新日志
tail -f logs/listing_tool.log
```

## 📞 技术支持

如果遇到问题：
1. 运行配置验证脚本
2. 查看程序日志文件
3. 检查JSON文件语法
4. 参考配置示例
5. 联系技术支持

---

**更新时间：** 2025-07-29  
**配置版本：** JSON v1.0.0
