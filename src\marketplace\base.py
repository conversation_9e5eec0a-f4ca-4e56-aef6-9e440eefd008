"""
商城基类
定义所有商城类型的通用接口和基础功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class MarketplaceConfig:
    """商城配置数据类"""
    name: str  # 商城名称
    display_name: str  # 显示名称
    template_file: str  # 默认模板文件
    product_file: str  # 默认产品资料文件
    target_file: str  # 默认目标文件
    supported_dimensions: List[str]  # 支持的父体维度
    default_dimension: str  # 默认父体维度
    update_modes: List[str]  # 支持的更新模式
    default_update_mode: str  # 默认更新模式


class BaseMarketplace(ABC):
    """商城基类"""
    
    def __init__(self, config: MarketplaceConfig):
        """
        初始化商城
        
        Args:
            config: 商城配置
        """
        self.config = config
        
    @abstractmethod
    def get_field_mapping(self, update_mode: str = 'partial') -> Dict[str, Any]:
        """
        获取字段映射关系
        
        Args:
            update_mode: 更新模式 ('partial' 或 'full')
            
        Returns:
            字段映射字典
        """
        pass
    
    @abstractmethod
    def get_default_values(self, record_type: str, update_mode: str = 'partial') -> Dict[str, Any]:
        """
        获取默认值配置
        
        Args:
            record_type: 记录类型 ('parent' 或 'child')
            update_mode: 更新模式 ('partial' 或 'full')
            
        Returns:
            默认值字典
        """
        pass
    
    @abstractmethod
    def generate_parent_sku(self, group_data: Dict[str, Any], dimension: str) -> str:
        """
        生成父体SKU
        
        Args:
            group_data: 分组数据
            dimension: 父体维度
            
        Returns:
            父体SKU
        """
        pass
    
    @abstractmethod
    def generate_item_name(self, size: str, keyword: str) -> str:
        """
        生成商品名称
        
        Args:
            size: 尺寸信息
            keyword: 商品关键词
            
        Returns:
            商品名称
        """
        pass
    
    @abstractmethod
    def validate_data(self, data: Dict[str, Any]) -> tuple[bool, str]:
        """
        验证数据
        
        Args:
            data: 待验证的数据
            
        Returns:
            (是否验证通过, 错误信息)
        """
        pass
    
    def get_template_start_row(self) -> Dict[str, int]:
        """
        获取模板填充起始行
        
        Returns:
            包含parent和child起始行的字典
        """
        return {
            'parent': 7,  # 父体起始行
            'child': 8    # 子体起始行
        }
    
    def get_template_header_row(self) -> int:
        """
        获取模板表头行

        Returns:
            表头行号（1基索引）
        """
        return 4  # 第4行是表头行
    
    def get_worksheet_name(self) -> str:
        """
        获取工作表名称
        
        Returns:
            工作表名称
        """
        return "Template"
