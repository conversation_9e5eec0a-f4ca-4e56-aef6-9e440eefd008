"""
配置管理器
统一管理应用程序配置和商城配置
"""

import json
import os
from typing import Dict, Any, Optional
from .settings import Settings


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.settings = Settings()
        self._user_config = {}
        self._config_file = os.path.join(self.settings.DATA_DIR, "config.json")
        self.load_user_config()
    
    def load_user_config(self):
        """加载用户配置"""
        try:
            if os.path.exists(self._config_file):
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    self._user_config = json.load(f)
            else:
                self._user_config = self._get_default_user_config()
                self.save_user_config()
        except Exception as e:
            print(f"加载用户配置失败: {e}")
            self._user_config = self._get_default_user_config()
    
    def save_user_config(self):
        """保存用户配置"""
        try:
            self.settings.ensure_directories()
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(self._user_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存用户配置失败: {e}")
    
    def _get_default_user_config(self) -> Dict[str, Any]:
        """获取默认用户配置"""
        return {
            "last_marketplace": self.settings.DEFAULT_MARKETPLACE,
            "last_update_mode": self.settings.DEFAULT_UPDATE_MODE,
            "last_dimension": self.settings.DEFAULT_DIMENSION,
            "window_geometry": self.settings.WINDOW_SIZE,
            "recent_files": {
                "target_files": [],
                "product_files": [],
                "template_files": []
            },
            "ui_preferences": {
                "theme": self.settings.UI_THEME,
                "font_size": self.settings.UI_FONT_SIZE_NORMAL,
                "show_advanced_options": False
            }
        }
    
    def get_user_config(self, key: str, default: Any = None) -> Any:
        """
        获取用户配置项
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._user_config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_user_config(self, key: str, value: Any):
        """
        设置用户配置项
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._user_config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        self.save_user_config()
    
    def get_marketplace_config(self, marketplace_type: str) -> Dict[str, Any]:
        """
        获取商城配置

        Args:
            marketplace_type: 商城类型

        Returns:
            商城配置字典
        """
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from marketplace.factory import MarketplaceFactory
        return MarketplaceFactory.get_marketplace_config(marketplace_type)

    def get_available_marketplaces(self) -> Dict[str, str]:
        """
        获取可用的商城类型

        Returns:
            商城类型字典
        """
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from marketplace.factory import MarketplaceFactory
        return MarketplaceFactory.get_available_marketplaces()
    
    def add_recent_file(self, file_type: str, file_path: str):
        """
        添加最近使用的文件
        
        Args:
            file_type: 文件类型 ('target_files', 'product_files', 'template_files')
            file_path: 文件路径
        """
        recent_files = self.get_user_config("recent_files", {})
        if file_type not in recent_files:
            recent_files[file_type] = []
        
        # 移除已存在的相同路径
        if file_path in recent_files[file_type]:
            recent_files[file_type].remove(file_path)
        
        # 添加到列表开头
        recent_files[file_type].insert(0, file_path)
        
        # 限制列表长度
        recent_files[file_type] = recent_files[file_type][:10]
        
        self.set_user_config("recent_files", recent_files)
    
    def get_recent_files(self, file_type: str) -> list:
        """
        获取最近使用的文件列表
        
        Args:
            file_type: 文件类型
            
        Returns:
            文件路径列表
        """
        recent_files = self.get_user_config("recent_files", {})
        return recent_files.get(file_type, [])
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self._user_config = self._get_default_user_config()
        self.save_user_config()
    
    def export_config(self, file_path: str):
        """
        导出配置到文件
        
        Args:
            file_path: 导出文件路径
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self._user_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise Exception(f"导出配置失败: {e}")
    
    def import_config(self, file_path: str):
        """
        从文件导入配置
        
        Args:
            file_path: 配置文件路径
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并配置（保留现有配置的结构）
            self._merge_config(self._user_config, imported_config)
            self.save_user_config()
        except Exception as e:
            raise Exception(f"导入配置失败: {e}")
    
    def _merge_config(self, target: dict, source: dict):
        """
        合并配置字典
        
        Args:
            target: 目标字典
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value
