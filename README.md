# 🛠️ 亚马逊listing拆分合并工具

一个用于自动生成亚马逊各商城listing模板的工具，支持低价商城、FBA商城等多种商城类型，提供GUI界面操作，简化listing创建流程。

## ✨ 功能特性

- 🔍 **商品关键词输入**: 支持手动输入商品关键词
- 📁 **文件管理**: 支持选择目标listing、产品资料和亚马逊模板文件
- ⚙️ **更新模式**: 支持部分更新和完全更新两种模式
- 🎯 **精准映射**: 所有字段采用精准匹配，确保数据准确性
- 🚀 **自动填充**: 自动生成父子体结构，包含所有必要的默认值
- 📊 **进度跟踪**: 实时显示处理进度和状态信息
- 💻 **现代化界面**: 基于tkinter的现代化GUI界面

## 📋 系统要求

- Python 3.8+
- Windows 11 (推荐)
- 8GB RAM (最低4GB)

## 🛠️ 安装和使用

### 1. 环境准备

```bash
# 克隆项目
git clone <项目地址>
cd 亚马逊listing拆分合并工具

# 安装依赖 (使用国内镜像源)
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 2. 启动程序

```bash
# 方法1: 直接运行主程序
python src/main.py

# 方法2: 运行GUI界面
python src/gui/main_window.py

# 方法3: 测试核心功能
python src/core/template_filler.py
```

### 3. 使用步骤

1. **输入商品关键词**: 在界面中输入商品关键词，例如 "satin ribbon"
2. **选择文件**（可选）: 
   - 目标listing表格 (默认: 已上架低价商城listing.xlsx)
   - 产品资料表格 (默认: 产品资料库-丝带-20250416.xlsx)
   - 亚马逊模板 (默认: 低价商城template.xlsm)
3. **选择更新模式**: 部分更新（推荐）或完全更新
4. **生成模板**: 点击"生成Listing模板"按钮
5. **查看结果**: 在`data/output/`目录中查看生成的Excel文件

## 📁 项目结构

```
亚马逊listing拆分合并工具/
├── src/                    # 源代码目录
│   ├── core/               # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── template_filler.py    # 模板填充器
│   │   └── data_analyzer.py      # 数据分析器
│   ├── gui/                # GUI界面
│   │   ├── __init__.py
│   │   └── main_window.py        # 主窗口
│   └── main.py             # 程序入口
├── data/                   # 数据目录
│   ├── input/              # 输入文件目录
│   ├── output/             # 输出文件目录
│   └── logs/               # 日志文件目录
├── docs/                   # 文档目录
│   ├── 亚马逊listing拆分合并工具需求文档.md  # 详细需求文档
│   ├── 字段映射关系.md       # 字段映射关系说明
│   ├── 项目结构说明.md       # 项目结构说明
│   └── 项目配置说明.md       # 配置参数说明
├── tests/                  # 测试目录
├── requirements.txt        # 项目依赖
└── README.md              # 项目说明
```

## 🔗 字段映射关系

### 核心映射
- `目标listing表格.MSKU` → `模板.Seller SKU`（子体）
- `目标listing表格.Product Description` → `模板.Product Description`
- `目标listing表格.color` → `模板.Color`

### 默认值配置

#### 父体字段
- Record Action: "Full Update"
- Product Type: "HOME"
- Brand Name: "GENERIC"
- Item Name: `size + 商品关键词`
- Parentage Level: "Parent"
- Variation Theme Name: "COLOR"
- Country of Origin: "China"

#### 子体字段
- Record Action: "Partial Update"
- Product Type: "HOME"
- Brand Name: "GENERIC"
- Parentage Level: "Child"
- Parent SKU: 引用父体Seller SKU
- Variation Theme Name: "COLOR"
- Country of Origin: "China"

## 📊 输入文件格式要求

### 1. 目标listing表格
- **文件格式**: .xlsx
- **工作表**: sheet1
- **表头位置**: 第1行
- **必要字段**: MSKU, SKU, color, size

### 2. 产品资料表格
- **文件格式**: .xlsx
- **工作表**: "单个产品"
- **表头位置**: 第1行
- **必要字段**: *SKU（必填）

### 3. 亚马逊模板
- **文件格式**: .xlsm 或 .xlsx
- **工作表**: "Template"
- **表头位置**: 第4行
- **填充位置**: 第7行开始（父体），第8行开始（子体）

## 🔧 高级功能

### 数据分析
运行数据分析器了解文件结构：
```bash
python src/core/data_analyzer.py
```

### 自定义文件路径
程序支持使用自定义文件路径，可以通过GUI界面选择不同的输入文件。

### 日志系统
程序会自动生成日志文件，保存在`data/logs/`目录中，方便问题排查。

## ❗ 注意事项

1. **精准匹配**: 所有字段之间采用精准匹配，不进行智能匹配
2. **数据完整性**: 确保输入文件包含所有必要字段
3. **编码格式**: 建议使用UTF-8编码保存Excel文件
4. **文件权限**: 确保对输出目录有写入权限

## 🆘 故障排除

### 常见问题

1. **导入模块失败**
   ```bash
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
   ```

2. **文件加载失败**
   - 检查文件路径是否正确
   - 确认文件格式符合要求
   - 检查文件是否被其他程序占用

3. **数据验证失败**
   - 确认必要字段存在
   - 检查工作表名称是否正确
   - 验证表头位置是否符合要求

4. **生成失败**
   - 查看状态信息中的错误详情
   - 检查日志文件获取更多信息
   - 确认输出目录存在且有写入权限

## 📝 更新日志

### v1.0.0 (2025-07-28)
- ✨ 初始版本发布
- 🎯 实现核心模板填充功能
- 💻 提供现代化GUI界面
- 📊 支持数据分析和验证
- 🔧 完整的字段映射关系
- 📁 支持自定义文件路径

## 👥 开发团队

- **亚马逊listing工具团队** - 项目开发和维护

## 📚 文档资源

### 完整文档列表
1. **📋 详细需求文档**: `docs/亚马逊listing拆分合并工具需求文档.md` - 完整的项目需求和技术规范
2. **🔗 字段映射关系**: `docs/字段映射关系.md` - 详细的字段映射关系说明
3. **📁 项目结构说明**: `docs/项目结构说明.md` - 项目目录结构和文件说明
4. **⚙️ 配置参数说明**: `docs/项目配置说明.md` - 系统配置和参数设置指南
5. **📊 程序日志**: `data/logs/` - 程序运行日志和错误信息

### 快速导航
- **新用户**: 建议先阅读 `README.md` 和 `docs/亚马逊listing拆分合并工具需求文档.md`
- **开发者**: 重点关注 `docs/项目结构说明.md` 和 `docs/项目配置说明.md`
- **问题排查**: 查看 `data/logs/` 目录中的日志文件

## 📚 文档资源

### 完整文档列表
1. **📋 详细需求文档**: `docs/亚马逊listing拆分合并工具需求文档.md` - 完整的项目需求和技术规范
2. **🔗 字段映射关系**: `docs/字段映射关系.md` - 详细的字段映射关系说明
3. **📁 项目结构说明**: `docs/项目结构说明.md` - 项目目录结构和文件说明
4. **⚙️ 配置参数说明**: `docs/项目配置说明.md` - 系统配置和参数设置指南
5. **📊 程序日志**: `data/logs/` - 程序运行日志和错误信息

### 快速导航
- **新用户**: 建议先阅读 `README.md` 和 `docs/亚马逊listing拆分合并工具需求文档.md`
- **开发者**: 重点关注 `docs/项目结构说明.md` 和 `docs/项目配置说明.md`
- **问题排查**: 查看 `data/logs/` 目录中的日志文件

## 📄 许可证

本项目采用内部许可证，仅供团队内部使用。