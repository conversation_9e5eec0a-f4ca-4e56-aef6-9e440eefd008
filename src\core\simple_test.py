"""
简单测试脚本
用于验证基本功能是否正常
"""

import pandas as pd
import os
from datetime import datetime

def simple_test():
    """简单测试函数"""
    print("🔍 开始简单测试...")
    
    # 测试基本的pandas功能
    print("📊 测试pandas基本功能...")
    test_data = {
        'MSKU': ['TEST001-RED', 'TEST001-BLUE', 'TEST002-RED'],
        'SKU': ['TEST001', 'TEST001', 'TEST002'],
        'color': ['Red', 'Blue', 'Red'],
        'size': ['3/8"-100 Yards', '3/8"-100 Yards', '1/2"-50 Yards'],
        'Product Description': ['测试产品1', '测试产品1', '测试产品2']
    }
    
    test_df = pd.DataFrame(test_data)
    print(f"✅ 测试数据创建成功: {len(test_df)}行")
    print(test_df)
    print()
    
    # 测试模板生成逻辑
    print("🚀 测试模板生成逻辑...")
    keyword = "satin ribbon"
    
    # 生成父子体结构
    skc_groups = {}
    child_data = []
    
    for _, row in test_df.iterrows():
        msku = row['MSKU']
        sku = row['SKU'] 
        color = row['color']
        size = row['size']
        
        # 按SKU分组（SKU作为SKC）
        if sku not in skc_groups:
            skc_groups[sku] = {
                'skc': sku,
                'size': size,
                'children': []
            }
        
        # 子体数据
        child_item = {
            'Seller SKU': msku,
            'Record Action': 'Partial Update',
            'Product Type': 'HOME',
            'Brand Name': 'GENERIC',
            'Color': color,
            'Parentage Level': 'Child',
            'Child Relationship Type': 'Variation',
            'Variation Theme Name': 'COLOR',
            'Country of Origin': 'China',
            'Are batteries required?': 'No',
            'Dangerous Goods Regulations': 'Not Applicable',
            'parent_skc': sku
        }
        child_data.append(child_item)
    
    # 父体数据
    parent_data = []
    for skc, group_info in skc_groups.items():
        size = group_info['size']
        item_name = f"{size} {keyword}"
        
        parent_item = {
            'Seller SKU': skc,
            'Record Action': 'Full Update',
            'Product Type': 'HOME',
            'Item Name': item_name,
            'Brand Name': 'GENERIC',
            'Item Type Keyword': keyword,
            'Product Description': item_name,
            'Bullet Point': item_name,
            'Parentage Level': 'Parent',
            'Child Relationship Type': 'Variation',
            'Variation Theme Name': 'COLOR',
            'Country of Origin': 'China',
            'Are batteries required?': 'No',
            'Dangerous Goods Regulations': 'Not Applicable'
        }
        parent_data.append(parent_item)
    
    print(f"✅ 父体生成成功: {len(parent_data)}个")
    print(f"✅ 子体生成成功: {len(child_data)}个")
    
    # 合并数据
    all_data = []
    for parent_item in parent_data:
        all_data.append(parent_item.copy())
        
        # 添加对应的子体
        for child_item in child_data:
            if child_item['parent_skc'] == parent_item['Seller SKU']:
                child_copy = child_item.copy()
                child_copy['Parent SKU'] = parent_item['Seller SKU']
                child_copy.pop('parent_skc', None)
                all_data.append(child_copy)
    
    # 创建输出数据框
    output_df = pd.DataFrame(all_data)
    print(f"✅ 输出数据框创建成功: {len(output_df)}行")
    
    # 保存到文件
    base_path = os.path.join(os.path.dirname(__file__), '..', '..')
    output_dir = os.path.join(base_path, 'data', 'output')
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"测试生成_{keyword}_{timestamp}.xlsx")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        output_df.to_excel(writer, sheet_name='Template', index=False)
    
    print(f"✅ 文件保存成功: {output_file}")
    
    # 显示结果预览
    print("\n📋 生成结果预览:")
    print(output_df[['Seller SKU', 'Record Action', 'Parentage Level', 'Color', 'Parent SKU']].head(10))
    
    return True

if __name__ == "__main__":
    try:
        success = simple_test()
        if success:
            print("\n🎉 简单测试全部通过！")
        else:
            print("\n❌ 测试失败")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc() 