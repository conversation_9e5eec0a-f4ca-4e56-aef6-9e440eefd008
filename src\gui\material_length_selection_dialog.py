"""
材质+长度选择对话框模块

该模块提供了一个对话框，用于让用户选择要处理的材质+长度组合。
当目标数据中包含多个不同的材质+长度组合时，用户可以通过此对话框选择具体要处理的组合。
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Optional, Set
from loguru import logger


class MaterialLengthSelectionDialog:
    """材质+长度选择对话框类"""
    
    def __init__(self, parent, material_length_groups: Dict[str, Dict]):
        """
        初始化材质+长度选择对话框
        
        Args:
            parent: 父窗口
            material_length_groups: 材质+长度组合字典
                格式: {group_key: {'material': str, 'length': str, 'widths': set}}
        """
        self.parent = parent
        self.material_length_groups = material_length_groups
        self.selected_group_key = None
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🎨 选择材质+长度组合")
        self.dialog.geometry("700x600")  # 增加高度确保按钮可见
        self.dialog.resizable(True, True)
        
        # 设置模态
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 绑定事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
        
    def _center_window(self):
        """将窗口居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
        
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主容器
        container = ttk.Frame(self.dialog)
        container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 内容区域
        content_frame = ttk.Frame(container)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 按钮区域 - 固定高度
        button_frame = ttk.Frame(container)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 标题
        title_label = ttk.Label(
            content_frame,
            text="🎯 材质+长度维度 - 宽度选择",
            font=("Microsoft YaHei", 16, "bold")
        )
        title_label.pack(pady=(0, 20))

        # 说明文本
        info_frame = ttk.Frame(content_frame)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        info_text = (
            "📋 准确信息\n\n"
            f"🏷️ 材质: {self._get_unique_materials()}\n"
            f"📏 长度: {self._get_unique_lengths()}\n\n"
            "请选择要合并为同一个父体的材质+长度组合。相同材质和长度的不同宽度规格将作为变体选项："
        )
        
        info_label = ttk.Label(
            info_frame, 
            text=info_text,
            font=("Microsoft YaHei", 10),
            justify=tk.LEFT
        )
        info_label.pack(anchor=tk.W)
        
        # 可用宽度规格标题 - 限制高度，不要expand=True
        available_frame = ttk.LabelFrame(content_frame, text="🔧 可用宽度规格", padding="10")
        available_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 创建滚动框架 - 设置固定高度
        canvas = tk.Canvas(available_frame, height=200)
        scrollbar = ttk.Scrollbar(available_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 组合选择单选按钮
        self.group_var = tk.StringVar()
        self.group_radios = {}
        
        for group_key, group_info in self.material_length_groups.items():
            material = group_info['material']
            length = group_info['length']
            widths = group_info['widths']
            
            # 创建组合框架
            group_frame = ttk.LabelFrame(
                scrollable_frame, 
                text=f"📦 {material} - {length}",
                padding="10"
            )
            group_frame.pack(fill=tk.X, pady=5)
            
            # 单选按钮
            radio = ttk.Radiobutton(
                group_frame,
                text=f"选择此组合 ({len(widths)} 个宽度规格)",
                variable=self.group_var,
                value=group_key,
                command=self._on_group_changed
            )
            radio.pack(anchor=tk.W, pady=(0, 5))
            self.group_radios[group_key] = radio
            
            # 显示宽度规格
            widths_text = "包含宽度规格: " + ", ".join(sorted(widths))
            widths_label = ttk.Label(
                group_frame,
                text=widths_text,
                font=("Microsoft YaHei", 9),
                foreground="gray"
            )
            widths_label.pack(anchor=tk.W)
        
        # 默认选择第一个组合
        if self.material_length_groups:
            first_key = next(iter(self.material_length_groups.keys()))
            self.group_var.set(first_key)
            self.selected_group_key = first_key

        # 创建底部按钮
        self._create_buttons(button_frame)

    def _create_buttons(self, button_frame):
        """创建底部按钮"""
        # 取消按钮
        cancel_btn = ttk.Button(
            button_frame,
            text="❌ 取消",
            command=self._on_cancel,
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 确定按钮
        confirm_btn = ttk.Button(
            button_frame,
            text="✅ 确定",
            command=self._on_confirm,
            width=15
        )
        confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 返回按钮
        back_btn = ttk.Button(
            button_frame,
            text="🔙 返回",
            command=self._on_back,
            width=15
        )
        back_btn.pack(side=tk.LEFT)

    def _get_unique_materials(self) -> str:
        """获取所有唯一材质"""
        materials = set()
        for group_info in self.material_length_groups.values():
            materials.add(group_info['material'])
        return ", ".join(sorted(materials))
        
    def _get_unique_lengths(self) -> str:
        """获取所有唯一长度"""
        lengths = set()
        for group_info in self.material_length_groups.values():
            lengths.add(group_info['length'])
        return ", ".join(sorted(lengths))
        
    def _on_group_changed(self):
        """组合选择改变时的回调"""
        self.selected_group_key = self.group_var.get()
        logger.debug(f"用户选择了组合: {self.selected_group_key}")
        
    def _on_confirm(self):
        """确定按钮点击事件"""
        logger.debug("🔘 确定按钮被点击")

        if not self.selected_group_key:
            logger.warning("⚠️ 没有选择任何组合")
            messagebox.showwarning("警告", "请选择一个材质+长度组合！")
            return

        logger.debug(f"📋 选择的组合键: {self.selected_group_key}")
        selected_group = self.material_length_groups[self.selected_group_key]
        logger.debug(f"📋 选择的组合信息: {selected_group}")

        self.result = {
            'confirmed': True,
            'selected_group_key': self.selected_group_key,
            'selected_group': selected_group,
            'action': 'confirm'
        }

        logger.info(f"✅ 用户确认选择组合: {self.selected_group_key}")
        logger.debug("🔄 正在关闭对话框...")
        self.dialog.destroy()
        
    def _on_cancel(self):
        """取消按钮点击事件"""
        self.result = {
            'confirmed': False,
            'selected_group_key': None,
            'selected_group': None,
            'action': 'cancel'
        }

        logger.info("用户取消了材质+长度组合选择")
        self.dialog.destroy()

    def _on_back(self):
        """返回按钮点击事件"""
        self.result = {
            'confirmed': False,
            'selected_group_key': None,
            'selected_group': None,
            'action': 'back'
        }

        logger.info("用户选择返回到维度选择")
        self.dialog.destroy()
        
    def show(self) -> Optional[Dict]:
        """
        显示对话框并等待用户操作
        
        Returns:
            用户选择结果，如果取消则返回None
        """
        # 等待对话框关闭
        self.dialog.wait_window()
        
        if self.result and self.result['confirmed']:
            return self.result
        else:
            return None


def show_material_length_selection_dialog(parent, material_length_groups: Dict[str, Dict]) -> Optional[Dict]:
    """
    显示材质+长度选择对话框的便捷函数
    
    Args:
        parent: 父窗口
        material_length_groups: 材质+长度组合字典
        
    Returns:
        用户选择结果，如果取消则返回None
    """
    dialog = MaterialLengthSelectionDialog(parent, material_length_groups)
    return dialog.show()
