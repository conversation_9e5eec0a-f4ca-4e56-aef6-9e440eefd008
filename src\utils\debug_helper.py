"""
调试辅助工具
提供统一的异常信息输出和调试功能
"""

import traceback
import sys
from typing import Optional, Any
from loguru import logger


class DebugHelper:
    """调试辅助类"""
    
    @staticmethod
    def log_exception(e: Exception, 
                     context: str = "未知错误", 
                     logger_instance: Optional[Any] = None,
                     print_to_console: bool = True,
                     show_full_traceback: bool = True) -> str:
        """
        统一的异常日志记录函数
        
        Args:
            e: 异常对象
            context: 错误上下文描述
            logger_instance: 日志记录器实例，默认使用全局logger
            print_to_console: 是否打印到控制台
            show_full_traceback: 是否显示完整的错误堆栈
            
        Returns:
            格式化的错误信息字符串
        """
        if logger_instance is None:
            logger_instance = logger
            
        # 构建错误信息
        error_type = type(e).__name__
        error_msg = str(e)
        
        # 获取错误堆栈
        if show_full_traceback:
            error_traceback = traceback.format_exc()
        else:
            error_traceback = traceback.format_exception_only(type(e), e)[-1].strip()
        
        # 格式化完整错误信息
        full_error_msg = f"""
{'='*60}
🚨 异常信息详情
{'='*60}
📍 错误上下文: {context}
🔍 异常类型: {error_type}
💬 错误消息: {error_msg}
📚 错误堆栈:
{error_traceback}
{'='*60}
"""
        
        # 记录到日志
        try:
            logger_instance.error(f"❌ {context}: {error_msg}")
            logger_instance.error(f"详细错误信息: {error_type}: {error_msg}")
            if show_full_traceback:
                logger_instance.error(f"错误堆栈:\n{error_traceback}")
        except Exception as log_error:
            # 如果日志记录失败，至少打印到控制台
            print(f"⚠️ 日志记录失败: {log_error}")
        
        # 打印到控制台
        if print_to_console:
            print(full_error_msg)
            # 强制刷新输出缓冲区
            import sys
            sys.stdout.flush()
            sys.stderr.flush()

            # 在Windows环境下，尝试直接写入到控制台
            try:
                import os
                if os.name == 'nt':  # Windows
                    # 尝试写入到控制台
                    import ctypes
                    kernel32 = ctypes.windll.kernel32
                    # 获取控制台句柄
                    console_handle = kernel32.GetStdHandle(-11)  # STD_OUTPUT_HANDLE
                    if console_handle and console_handle != -1:
                        # 将错误信息编码为字节
                        error_bytes = full_error_msg.encode('utf-8', errors='ignore')
                        bytes_written = ctypes.c_ulong(0)
                        kernel32.WriteConsoleA(console_handle, error_bytes, len(error_bytes), ctypes.byref(bytes_written), None)
            except Exception:
                pass  # 如果失败，忽略这个额外的输出尝试
            
        return full_error_msg
    
    @staticmethod
    def log_debug_info(message: str, 
                      data: Optional[Any] = None,
                      logger_instance: Optional[Any] = None,
                      print_to_console: bool = True) -> None:
        """
        记录调试信息
        
        Args:
            message: 调试消息
            data: 相关数据（可选）
            logger_instance: 日志记录器实例
            print_to_console: 是否打印到控制台
        """
        if logger_instance is None:
            logger_instance = logger
            
        debug_msg = f"🔧 调试信息: {message}"
        
        if data is not None:
            debug_msg += f"\n📊 相关数据: {data}"
            
        try:
            logger_instance.debug(debug_msg)
        except Exception as log_error:
            print(f"⚠️ 调试日志记录失败: {log_error}")
            
        if print_to_console:
            print(debug_msg)
            # 强制刷新输出缓冲区
            import sys
            sys.stdout.flush()
    
    @staticmethod
    def log_function_entry(func_name: str, 
                          args: Optional[tuple] = None,
                          kwargs: Optional[dict] = None,
                          logger_instance: Optional[Any] = None) -> None:
        """
        记录函数进入信息
        
        Args:
            func_name: 函数名
            args: 位置参数
            kwargs: 关键字参数
            logger_instance: 日志记录器实例
        """
        if logger_instance is None:
            logger_instance = logger
            
        entry_msg = f"🚀 进入函数: {func_name}"
        
        if args:
            entry_msg += f"\n📥 位置参数: {args}"
            
        if kwargs:
            entry_msg += f"\n🔑 关键字参数: {kwargs}"
            
        try:
            logger_instance.debug(entry_msg)
        except Exception as log_error:
            print(f"⚠️ 函数进入日志记录失败: {log_error}")
    
    @staticmethod
    def log_function_exit(func_name: str, 
                         result: Optional[Any] = None,
                         logger_instance: Optional[Any] = None) -> None:
        """
        记录函数退出信息
        
        Args:
            func_name: 函数名
            result: 返回结果
            logger_instance: 日志记录器实例
        """
        if logger_instance is None:
            logger_instance = logger
            
        exit_msg = f"✅ 退出函数: {func_name}"
        
        if result is not None:
            exit_msg += f"\n📤 返回结果: {result}"
            
        try:
            logger_instance.debug(exit_msg)
        except Exception as log_error:
            print(f"⚠️ 函数退出日志记录失败: {log_error}")
    
    @staticmethod
    def create_debug_decorator(logger_instance: Optional[Any] = None):
        """
        创建调试装饰器
        
        Args:
            logger_instance: 日志记录器实例
            
        Returns:
            调试装饰器函数
        """
        def debug_decorator(func):
            def wrapper(*args, **kwargs):
                func_name = func.__name__
                
                # 记录函数进入
                DebugHelper.log_function_entry(func_name, args, kwargs, logger_instance)
                
                try:
                    # 执行函数
                    result = func(*args, **kwargs)
                    
                    # 记录函数退出
                    DebugHelper.log_function_exit(func_name, result, logger_instance)
                    
                    return result
                    
                except Exception as e:
                    # 记录异常
                    DebugHelper.log_exception(
                        e, 
                        f"函数 {func_name} 执行时出错",
                        logger_instance
                    )
                    raise
                    
            return wrapper
        return debug_decorator


# 便捷函数
def log_exception(e: Exception, context: str = "未知错误", **kwargs) -> str:
    """便捷的异常日志记录函数"""
    return DebugHelper.log_exception(e, context, **kwargs)


def log_debug(message: str, data: Optional[Any] = None, **kwargs) -> None:
    """便捷的调试信息记录函数"""
    DebugHelper.log_debug_info(message, data, **kwargs)


def debug_function(logger_instance: Optional[Any] = None):
    """便捷的调试装饰器"""
    return DebugHelper.create_debug_decorator(logger_instance)


# 全局调试开关
DEBUG_MODE = True

def set_debug_mode(enabled: bool) -> None:
    """设置调试模式"""
    global DEBUG_MODE
    DEBUG_MODE = enabled
    print(f"🔧 调试模式: {'开启' if enabled else '关闭'}")


def is_debug_mode() -> bool:
    """检查是否为调试模式"""
    return DEBUG_MODE
