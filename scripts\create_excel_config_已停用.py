#!/usr/bin/env python3
"""
创建Excel格式的字段映射配置文件
让程序小白也能轻松维护配置
"""

import pandas as pd
import os
from pathlib import Path

def create_excel_config():
    """创建Excel配置文件"""
    print('📊 创建Excel配置文件...')
    
    # 确保目录存在
    config_dir = Path('data/config')
    config_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建FBA商城字段映射表 - 基于DECORATIVE_RIBBON_TRIM_US.xlsm模板
    fba_fields = [
        {
            '模板字段名': 'Seller SKU',
            '模板列号': 'B列(2)',
            '父体如何填充': '程序自动生成（FBA-前缀+SKC）',
            '子体如何填充': '来自→目标listing表格（用户上传）→MSKU列（加FBA-前缀）',
            '数据来源表格': '目标listing表格（用户上传）',
            '数据来源列名': 'MSKU',
            '填充示例': 'FBA-DL-LW-PZ-PT-02-050',
            '字段说明': '卖家SKU，FBA商城需要加FBA-前缀',
            '程序字段名': 'item_sku'
        },
        {
            '模板字段名': 'Brand',
            '模板列号': 'C列(3)',
            '父体如何填充': '固定填写→GENERIC',
            '子体如何填充': '固定填写→GENERIC',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'GENERIC',
            '字段说明': '品牌名称，统一使用GENERIC',
            '程序字段名': 'brand_name'
        },
        {
            '模板字段名': 'Update Delete',
            '模板列号': 'D列(4)',
            '父体如何填充': '固定填写→Update',
            '子体如何填充': '固定填写→PartialUpdate',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'Update / PartialUpdate',
            '字段说明': '更新类型，父体用Update，子体用PartialUpdate',
            '程序字段名': 'update_delete'
        },
        {
            '模板字段名': 'Title',
            '模板列号': 'G列(7)',
            '父体如何填充': '程序自动生成',
            '子体如何填充': '程序自动生成',
            '数据来源表格': '程序根据尺寸和关键词组合',
            '数据来源列名': '自动组合',
            '填充示例': 'FBA 3/8"-100 Yards satin ribbon',
            '字段说明': '商品标题，程序根据尺寸和关键词自动生成',
            '程序字段名': 'item_name'
        },
        {
            '模板字段名': 'Product Description',
            '模板列号': 'J列(10)',
            '父体如何填充': '来自→已上架低价商城listing.xlsx→品名列',
            '子体如何填充': '来自→目标listing表格（用户上传）→品名列',
            '数据来源表格': '目标listing表格（用户上传）',
            '数据来源列名': '品名',
            '填充示例': '3/8英寸缎带丝带',
            '字段说明': '产品描述，从现有数据的品名列获取',
            '程序字段名': 'product_description'
        },
        {
            '模板字段名': 'Your Price',
            '模板列号': 'N列(14)',
            '父体如何填充': '不填充',
            '子体如何填充': '来自→目标listing表格（用户上传）→price列',
            '数据来源表格': '目标listing表格（用户上传）',
            '数据来源列名': 'price',
            '填充示例': '9.99',
            '字段说明': '销售价格，只有子体需要填充，从price列获取',
            '程序字段名': 'standard_price'
        },
        {
            '模板字段名': 'Quantity',
            '模板列号': 'O列(15)',
            '父体如何填充': '不填充',
            '子体如何填充': '来自→目标listing表格（用户上传）→quantity列',
            '数据来源表格': '目标listing表格（用户上传）',
            '数据来源列名': 'quantity',
            '填充示例': '100',
            '字段说明': '库存数量，只有子体需要填充，从quantity列获取',
            '程序字段名': 'quantity'
        },
        {
            '模板字段名': 'Parentage',
            '模板列号': 'Z列(26)',
            '父体如何填充': '固定填写→Parent',
            '子体如何填充': '固定填写→Child',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'Parent / Child',
            '字段说明': '父子关系标识，父体填Parent，子体填Child',
            '程序字段名': 'parent_child'
        },
        {
            '模板字段名': 'Parent SKU',
            '模板列号': 'AA列(27)',
            '父体如何填充': '不填充',
            '子体如何填充': '引用父体的SKU',
            '数据来源表格': '引用父体生成的SKU',
            '数据来源列名': '父体SKU',
            '填充示例': 'FBA-DL-LW-PZ-PT-02-050',
            '字段说明': '父体SKU引用，只有子体需要填充，引用对应父体的SKU',
            '程序字段名': 'parent_sku'
        },
        {
            '模板字段名': 'Color',
            '模板列号': 'AM列(39)',
            '父体如何填充': '不填充',
            '子体如何填充': '来自→目标listing表格（用户上传）→color列',
            '数据来源表格': '目标listing表格（用户上传）',
            '数据来源列名': 'color',
            '填充示例': 'Red',
            '字段说明': '颜色信息，只有子体需要填充，从color列获取',
            '程序字段名': 'color_name'
        },
        {
            '模板字段名': 'Size',
            '模板列号': 'AR列(44)',
            '父体如何填充': '不填充',
            '子体如何填充': '来自→目标listing表格（用户上传）→size列',
            '数据来源表格': '目标listing表格（用户上传）',
            '数据来源列名': 'size',
            '填充示例': '3/8"-100 Yards',
            '字段说明': '尺寸信息，只有子体需要填充，从size列获取',
            '程序字段名': 'size_name'
        },
        {
            '模板字段名': 'Country/Region of Origin',
            '模板列号': 'EJ列(134)',
            '父体如何填充': '固定填写→China',
            '子体如何填充': '固定填写→China',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'China',
            '字段说明': '原产国，统一填写China',
            '程序字段名': 'country_of_origin'
        }
    ]
    
    # 创建低价商城字段映射表 - 根据需求文档重新设计
    low_price_fields = [
        # 部分更新 - 父体必要字段
        {
            '模板字段名': 'Seller SKU',
            '模板列号': '待确认',
            '父体如何填充': '程序自动生成（基于SKC维度）',
            '子体如何填充': '来自→目标listing表格（用户上传）→MSKU列',
            '数据来源表格': '目标listing表格（用户上传）',
            '数据来源列名': 'MSKU',
            '填充示例': '父体:SKC值 / 子体:DL-LW-PZ-PT-02-050',
            '字段说明': '卖家SKU，父体基于SKC维度生成，子体直接使用MSKU',
            '程序字段名': 'seller_sku'
        },
        {
            '模板字段名': 'Record Action',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→Full Update',
            '子体如何填充': '固定填写→Partial Update',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'Full Update / Partial Update',
            '字段说明': '记录操作类型，父体用Full Update，子体用Partial Update',
            '程序字段名': 'record_action'
        },
        {
            '模板字段名': 'Product Type',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→HOME',
            '子体如何填充': '固定填写→HOME',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'HOME',
            '字段说明': '产品类型，统一使用HOME',
            '程序字段名': 'product_type'
        },
        {
            '模板字段名': 'Item Name',
            '模板列号': '待确认',
            '父体如何填充': '程序自动生成（size + 商品关键词）',
            '子体如何填充': '不填充',
            '数据来源表格': '目标listing表格（用户上传） + 用户输入',
            '数据来源列名': 'size + 商品关键词',
            '填充示例': '3/8"-100 Yards satin ribbon',
            '字段说明': '商品名称，父体由size和关键词组合生成',
            '程序字段名': 'item_name'
        },
        {
            '模板字段名': 'Brand Name',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→GENERIC',
            '子体如何填充': '固定填写→GENERIC',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'GENERIC',
            '字段说明': '品牌名称，统一使用GENERIC',
            '程序字段名': 'brand_name'
        },
        {
            '模板字段名': 'Item Type Keyword',
            '模板列号': '待确认',
            '父体如何填充': '用户输入的商品关键词',
            '子体如何填充': '不填充',
            '数据来源表格': '用户输入',
            '数据来源列名': '商品关键词',
            '填充示例': 'satin ribbon',
            '字段说明': '商品类型关键词，使用用户输入的关键词',
            '程序字段名': 'item_type_keyword'
        },
        {
            '模板字段名': 'Product Description',
            '模板列号': '待确认',
            '父体如何填充': '程序自动生成（等于Item Name）',
            '子体如何填充': '不填充',
            '数据来源表格': '生成的Item Name',
            '数据来源列名': 'Item Name',
            '填充示例': '3/8"-100 Yards satin ribbon',
            '字段说明': '产品描述，父体等于Item Name',
            '程序字段名': 'product_description'
        },
        {
            '模板字段名': 'Bullet Point',
            '模板列号': '待确认',
            '父体如何填充': '程序自动生成（等于Item Name）',
            '子体如何填充': '不填充',
            '数据来源表格': '生成的Item Name',
            '数据来源列名': 'Item Name',
            '填充示例': '3/8"-100 Yards satin ribbon',
            '字段说明': '要点描述，父体等于Item Name',
            '程序字段名': 'bullet_point'
        },
        {
            '模板字段名': 'Parentage Level',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→Parent',
            '子体如何填充': '固定填写→Child',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'Parent / Child',
            '字段说明': '父子关系级别，父体填Parent，子体填Child',
            '程序字段名': 'parentage_level'
        },
        {
            '模板字段名': 'Child Relationship Type',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→Variation',
            '子体如何填充': '固定填写→Variation',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'Variation',
            '字段说明': '子关系类型，统一使用Variation',
            '程序字段名': 'child_relationship_type'
        },
        {
            '模板字段名': 'Parent SKU',
            '模板列号': '待确认',
            '父体如何填充': '不填充',
            '子体如何填充': '引用对应父体的Seller SKU',
            '数据来源表格': '生成的父体Seller SKU',
            '数据来源列名': '父体Seller SKU',
            '填充示例': 'SKC值',
            '字段说明': '父体SKU引用，子体引用对应父体的Seller SKU',
            '程序字段名': 'parent_sku'
        },
        {
            '模板字段名': 'Variation Theme Name',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→COLOR',
            '子体如何填充': '固定填写→COLOR',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'COLOR',
            '字段说明': '变体主题名称，统一使用COLOR',
            '程序字段名': 'variation_theme_name'
        },
        {
            '模板字段名': 'Color',
            '模板列号': '待确认',
            '父体如何填充': '不填充',
            '子体如何填充': '来自→目标listing表格（用户上传）→color列',
            '数据来源表格': '目标listing表格（用户上传）',
            '数据来源列名': 'color',
            '填充示例': 'Red',
            '字段说明': '颜色信息，子体从现有数据的color列获取',
            '程序字段名': 'color'
        },
        {
            '模板字段名': 'Country of Origin',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→China',
            '子体如何填充': '固定填写→China',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'China',
            '字段说明': '原产国，统一使用China',
            '程序字段名': 'country_of_origin'
        },
        {
            '模板字段名': 'Are batteries required?',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→No',
            '子体如何填充': '固定填写→No',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'No',
            '字段说明': '是否需要电池，统一填No',
            '程序字段名': 'batteries_required'
        },
        {
            '模板字段名': 'Dangerous Goods Regulations',
            '模板列号': '待确认',
            '父体如何填充': '固定填写→Not Applicable',
            '子体如何填充': '固定填写→Not Applicable',
            '数据来源表格': '无需数据源',
            '数据来源列名': '固定值',
            '填充示例': 'Not Applicable',
            '字段说明': '危险品规定，统一填Not Applicable',
            '程序字段名': 'dangerous_goods_regulations'
        }
    ]
    
    # 数据来源说明
    data_sources = [
        {'数据文件名': '目标listing表格（用户上传）', '文件位置': '用户通过GUI界面选择上传', '工作表名': 'Sheet1或用户指定', '用途': '主要数据源，包含MSKU、color、size、品名等产品信息'},
        {'数据文件名': '产品资料库-丝带-20250416.xlsx', '文件位置': 'data/input/', '工作表名': '单个产品', '用途': '产品基础资料，包含SKC等信息，用于SKC维度匹配'},
        {'数据文件名': 'DECORATIVE_RIBBON_TRIM_US.xlsm', '文件位置': 'data/input/', '工作表名': 'Template', '用途': 'FBA商城上传模板'},
        {'数据文件名': '低价商城template.xlsm', '文件位置': 'data/input/', '工作表名': 'Template', '用途': '低价商城上传模板'},
        {'数据文件名': '用户输入的商品关键词', '文件位置': 'GUI界面输入', '工作表名': '不适用', '用途': '用于生成Item Name和Item Type Keyword'}
    ]

    # 字段值类型说明 - 根据需求文档更新
    field_types = [
        {'值类型': '🔧 如何填充字段', '说明': '详细说明（基于项目需求文档）', '示例': ''},
        {'值类型': '程序自动生成', '说明': '由程序根据业务规则自动生成', '示例': 'SKC维度父体SKU、Item Name等'},
        {'值类型': '程序自动生成（基于SKC维度）', '说明': '父体SKU基于SKC维度匹配生成', '示例': '通过目标listing.SKU匹配产品资料.SKC'},
        {'值类型': '程序自动生成（size + 商品关键词）', '说明': 'Item Name由size和用户输入关键词组合', '示例': '3/8"-100 Yards + satin ribbon'},
        {'值类型': '程序自动生成（等于Item Name）', '说明': 'Product Description和Bullet Point等于Item Name', '示例': '与Item Name保持一致'},
        {'值类型': '固定填写→具体值', '说明': '每次都填写相同的固定值', '示例': '固定填写→GENERIC、固定填写→China'},
        {'值类型': '来自→文件名→列名', '说明': '从指定Excel文件的指定列获取数据', '示例': '来自→目标listing表格（用户上传）→MSKU列'},
        {'值类型': '引用对应父体的Seller SKU', '说明': '子体Parent SKU字段引用对应父体的Seller SKU', '示例': '子体引用父体SKU建立关联'},
        {'值类型': '用户输入的商品关键词', '说明': '使用用户在GUI界面输入的商品关键词', '示例': 'satin ribbon'},
        {'值类型': '不填充', '说明': '该字段留空，不填写任何内容', '示例': '某些字段仅父体或仅子体填充'},
        {'值类型': '', '说明': '', '示例': ''},
        {'值类型': '📊 数据来源文件说明（需求文档定义）', '说明': '', '示例': ''},
        {'值类型': '目标listing表格（用户上传）', '说明': '用户通过GUI界面上传的Excel文件，包含MSKU、color、size、品名、price等字段', '示例': 'MSKU: DL-LW-PZ-PT-02-050'},
        {'值类型': '产品资料库-丝带-20250416.xlsx', '说明': '产品资料表格，包含SKU、SKC、品名等基础资料', '示例': 'SKC: DL-LW-PZ-PT'},
        {'值类型': '低价商城template.xlsm', '说明': '亚马逊低价商城模板，表头第4行，填充从第7行开始', '示例': '288列字段'},
        {'值类型': 'DECORATIVE_RIBBON_TRIM_US.xlsm', '说明': 'FBA商城模板，表头第2行，填充从第5行开始', '示例': '211列字段'},
        {'值类型': '', '说明': '', '示例': ''},
        {'值类型': '💡 业务逻辑说明', '说明': '', '示例': ''},
        {'值类型': 'SKC维度父体生成', '说明': 'SKC相同的Seller SKU归为一个父体', '示例': '通过目标listing.SKU匹配产品资料.SKC'},
        {'值类型': '父子体关联关系', '说明': '子体Parent SKU引用对应父体Seller SKU', '示例': '建立父子体关联'},
        {'值类型': '部分更新vs完全更新', '说明': '完全更新在部分更新基础上增加Color等字段', '示例': '根据更新模式决定填充字段'},
        {'值类型': '', '说明': '', '示例': ''},
        {'值类型': '⚠️ 重要注意事项', '说明': '', '示例': ''},
        {'值类型': '严格按需求文档执行', '说明': '所有字段映射必须符合项目需求文档定义', '示例': ''},
        {'值类型': '精准匹配原则', '说明': '所有字段采用精准匹配，不进行模糊匹配', '示例': ''},
        {'值类型': '数据完整性检查', '说明': '确保所有必要字段都有对应数据源或默认值', '示例': ''}
    ]
    
    # 创建Excel文件
    excel_path = 'data/config/字段映射配置.xlsx'
    
    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # FBA商城字段映射
            fba_df = pd.DataFrame(fba_fields)
            fba_df.to_excel(writer, sheet_name='FBA商城字段映射', index=False)
            
            # 低价商城字段映射
            low_price_df = pd.DataFrame(low_price_fields)
            low_price_df.to_excel(writer, sheet_name='低价商城字段映射', index=False)
            
            # 数据来源说明
            data_sources_df = pd.DataFrame(data_sources)
            data_sources_df.to_excel(writer, sheet_name='数据来源说明', index=False)

            # 字段值类型说明
            field_types_df = pd.DataFrame(field_types)
            field_types_df.to_excel(writer, sheet_name='配置说明', index=False)
        
        print(f'✅ Excel配置文件已创建: {excel_path}')
        print('📝 您现在可以用Excel打开这个文件进行编辑了！')
        
        # 设置列宽（如果可能的话）
        try:
            from openpyxl import load_workbook
            wb = load_workbook(excel_path)
            
            # 调整FBA商城工作表的列宽
            if 'FBA商城字段映射' in wb.sheetnames:
                ws = wb['FBA商城字段映射']
                ws.column_dimensions['A'].width = 20  # 字段名
                ws.column_dimensions['B'].width = 8   # 列号
                ws.column_dimensions['C'].width = 30  # 字段显示名
                ws.column_dimensions['D'].width = 25  # 父体值
                ws.column_dimensions['E'].width = 25  # 子体值
                ws.column_dimensions['F'].width = 50  # 描述
            
            # 调整低价商城工作表的列宽
            if '低价商城字段映射' in wb.sheetnames:
                ws = wb['低价商城字段映射']
                ws.column_dimensions['A'].width = 20  # 字段名
                ws.column_dimensions['B'].width = 8   # 列号
                ws.column_dimensions['C'].width = 30  # 字段显示名
                ws.column_dimensions['D'].width = 25  # 父体值
                ws.column_dimensions['E'].width = 25  # 子体值
                ws.column_dimensions['F'].width = 50  # 描述
            
            # 调整配置说明工作表的列宽
            if '配置说明' in wb.sheetnames:
                ws = wb['配置说明']
                ws.column_dimensions['A'].width = 25  # 项目
                ws.column_dimensions['B'].width = 60  # 说明
            
            wb.save(excel_path)
            print('📐 已优化Excel表格列宽，便于查看')
            
        except ImportError:
            print('💡 提示: 安装openpyxl可以获得更好的Excel格式支持')
        except Exception as e:
            print(f'⚠️ 优化Excel格式时出现问题: {e}')
        
        return True
        
    except Exception as e:
        print(f'❌ 创建Excel配置文件失败: {e}')
        return False

if __name__ == "__main__":
    create_excel_config()
