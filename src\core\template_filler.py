"""
模板填充核心逻辑
支持多种商城类型的listing模板自动填充功能
"""

import pandas as pd
import os
from typing import Dict, List, Tuple, Any, Optional
from loguru import logger
import shutil
from datetime import datetime

# 导入尺寸解析工具和调试工具
try:
    from ..utils.width_parser import SizeParser
    from ..utils.variant_parser import VariantAttributeParser
    from ..utils.debug_helper import log_exception, log_debug, debug_function
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from utils.width_parser import SizeParser
    from utils.variant_parser import VariantAttributeParser
    from utils.debug_helper import log_exception, log_debug, debug_function


class TemplateFiller:
    """模板填充器类 - 支持多商城类型"""

    def __init__(self, marketplace=None):
        """
        初始化模板填充器

        Args:
            marketplace: 商城实例，如果为None则使用默认低价商城
        """
        self.base_path = os.path.join(os.path.dirname(__file__), '..', '..')
        self.marketplace = marketplace

        # 如果没有提供商城实例，使用默认低价商城
        if self.marketplace is None:
            import sys
            current_file_dir = os.path.dirname(__file__)
            sys.path.append(os.path.dirname(current_file_dir))
            from marketplace.factory import MarketplaceFactory
            self.marketplace = MarketplaceFactory.create_marketplace('low_price')

        # 数据存储
        self.target_listing_df = None
        self.product_data_df = None
        self.template_df = None

        # 字段名映射表（配置字段名 -> 模板字段名）
        self.field_name_mapping = {
            'seller_sku': 'Seller SKU',
            'record_action': 'Record Action',
            'product_type': 'Product Type',
            'item_name': 'Item Name',
            'brand_name': 'Brand Name',
            'item_type_keyword': 'Item Type Keyword',
            'product_description': 'Product Description',
            'bullet_point': 'Bullet Point',
            'parentage_level': 'Parentage Level',
            'child_relationship_type': 'Child Relationship Type',
            'parent_sku': 'Parent SKU',
            'variation_theme_name': 'Variation Theme Name',
            'color': 'Color',
            'country_of_origin': 'Country of Origin',
            'batteries_required': 'Are batteries required?',
            'dangerous_goods_regulations': 'Dangerous Goods Regulations'
        }

    def convert_field_names(self, data_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        将配置中的字段名转换为模板中的标准字段名

        Args:
            data_dict: 包含配置字段名的数据字典

        Returns:
            转换后的数据字典
        """
        converted_dict = {}
        for key, value in data_dict.items():
            # 对于FBA商城，直接使用配置中的字段名，不进行转换
            # 因为FBA的配置已经使用了正确的模板字段名
            if self.marketplace and hasattr(self.marketplace, 'config') and self.marketplace.config.name == 'fba':
                converted_dict[key] = value
            else:
                # 其他商城使用字段名映射
                template_field_name = self.field_name_mapping.get(key, key)
                converted_dict[template_field_name] = value
        return converted_dict

    def apply_field_mapping(self, data_dict: Dict[str, Any], record_type: str,
                           keyword: str, source_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        应用字段映射逻辑，处理特殊值

        Args:
            data_dict: 原始数据字典
            record_type: 记录类型 ('parent' 或 'child')
            keyword: 商品关键词
            source_data: 源数据（用于映射）

        Returns:
            处理后的数据字典
        """
        processed_dict = data_dict.copy()

        for field_name, field_value in processed_dict.items():
            if isinstance(field_value, str):
                # 处理特殊值
                if field_value == 'generated':
                    # 根据字段类型生成相应的值
                    if field_name in ['Seller SKU', 'Item Name', 'Title']:
                        if record_type == 'parent':
                            # 父体SKU生成逻辑
                            if field_name == 'Seller SKU' and source_data and 'SKC' in source_data:
                                processed_dict[field_name] = self.marketplace.generate_parent_sku(source_data, 'SKC')
                            # 父体标题生成逻辑
                            elif field_name in ['Item Name', 'Title'] and source_data and 'size' in source_data:
                                processed_dict[field_name] = self.marketplace.generate_item_name(source_data['size'], keyword)
                        # 子体SKU在其他地方处理
                    elif field_name == 'Product Description':
                        if source_data and 'size' in source_data:
                            processed_dict[field_name] = self.marketplace.generate_item_name(source_data['size'], keyword)
                    elif field_name == 'Bullet Point':
                        if source_data and 'size' in source_data:
                            processed_dict[field_name] = self.marketplace.generate_item_name(source_data['size'], keyword)

                elif field_value == 'generated_smart':
                    # 智能生成父体SKU
                    if field_name == 'Seller SKU' and record_type == 'parent':
                        if source_data and 'SKC' in source_data:
                            processed_dict[field_name] = self.marketplace.generate_parent_sku(source_data, 'SKC', keyword)

                elif field_value == 'generated_smart_title':
                    # 智能生成父体标题（根据实际组合情况）
                    if field_name == 'Title' and record_type == 'parent':
                        if source_data and 'size' in source_data:
                            # 根据实际组合情况智能生成标题
                            processed_dict[field_name] = self._generate_smart_title(source_data, keyword)

                elif field_value == 'generated_same_as_title':
                    # Product Description等于Title
                    if field_name == 'Product Description' and record_type == 'parent':
                        title_value = processed_dict.get('Title', '')
                        if not title_value and source_data and 'size' in source_data:
                            title_value = self._generate_smart_title(source_data, keyword)
                        processed_dict[field_name] = title_value

                elif field_value == 'smart_parse_color':
                    # 智能解析颜色
                    if source_data:
                        # 优先从变体属性列解析
                        if '变体属性' in source_data:
                            processed_dict[field_name] = self._parse_color_from_variant(source_data['变体属性'])
                        elif 'color' in source_data:
                            processed_dict[field_name] = source_data['color']

                elif field_value == 'smart_parse_size':
                    # 智能解析尺寸，统一格式
                    if source_data and 'size' in source_data:
                        processed_dict[field_name] = self._format_size(source_data['size'])
                    else:
                        processed_dict[field_name] = ""

                elif field_value == 'smart_parse_size_with_symbol':
                    # 智能解析尺寸，使用符号"显示
                    if source_data and 'size' in source_data:
                        processed_dict[field_name] = self._format_size_with_symbol(source_data['size'])
                    else:
                        processed_dict[field_name] = ""

                elif field_value == 'generated_parent_sku':
                    # 引用父体SKU（用于子体的Parent SKU字段）
                    # 这个值在后续处理中设置
                    pass

                elif field_value == '用户输入的商品关键词':
                    processed_dict[field_name] = keyword

                elif field_value.startswith('target_listing.'):
                    # 从目标listing数据中获取值
                    field_key = field_value.replace('target_listing.', '')
                    if source_data and field_key in source_data:
                        processed_dict[field_name] = source_data[field_key]
                    else:
                        # 如果没有找到对应的数据，保持空值
                        processed_dict[field_name] = ""

                elif field_value == 'from_data_brand':
                    # Brand字段特殊处理 - 使用统一的品牌名称
                    if field_name == 'Brand' and hasattr(self, 'unified_brand') and self.unified_brand:
                        processed_dict[field_name] = self.unified_brand
                        logger.debug(f"✅ Brand字段填充: '{self.unified_brand}'")
                    else:
                        processed_dict[field_name] = ""
                        logger.debug(f"⚠️ Brand字段为空: 没有统一品牌名或字段不匹配")

                elif field_value == 'from_data':
                    # 从源数据中获取对应字段的值
                    if field_name == 'Product ID' and record_type == 'child':
                        # 子体Product ID需要通过Seller SKU匹配MSKU获取ASIN
                        seller_sku = processed_dict.get('Seller SKU', '')
                        if seller_sku and hasattr(self, 'target_listing_df') and self.target_listing_df is not None:
                            # 在目标表格中查找匹配的MSKU
                            matching_row = self.target_listing_df[self.target_listing_df['MSKU'] == seller_sku]
                            if not matching_row.empty:
                                asin = matching_row.iloc[0].get('ASIN', '')
                                processed_dict[field_name] = asin
                            else:
                                processed_dict[field_name] = ""
                        else:
                            processed_dict[field_name] = ""
                    else:
                        # 如果没有找到对应的数据，保持空值
                        processed_dict[field_name] = ""

                elif field_value == '引用对应父体的Seller SKU':
                    # 这个值在后续处理中设置
                    pass

        return processed_dict

    def _parse_color_from_variant(self, variant_attr: str) -> str:
        """
        从变体属性中智能解析颜色

        Args:
            variant_attr: 变体属性字符串

        Returns:
            解析出的颜色
        """
        if not variant_attr or pd.isna(variant_attr):
            return ""

        # 简单的颜色解析逻辑，可以根据实际需要扩展
        variant_str = str(variant_attr).lower()

        # 常见颜色映射
        color_mapping = {
            'red': 'Red',
            'blue': 'Blue',
            'green': 'Green',
            'yellow': 'Yellow',
            'black': 'Black',
            'white': 'White',
            'pink': 'Pink',
            'purple': 'Purple',
            'orange': 'Orange',
            'brown': 'Brown',
            'gray': 'Gray',
            'grey': 'Gray'
        }

        for key, value in color_mapping.items():
            if key in variant_str:
                return value

        # 如果没有匹配到，返回原始值的首字母大写形式
        return str(variant_attr).strip().title()

    def _format_size(self, size: str) -> str:
        """
        智能格式化尺寸，统一格式

        Args:
            size: 原始尺寸字符串

        Returns:
            格式化后的尺寸
        """
        if not size or pd.isna(size):
            return ""

        size_str = str(size).strip()

        # 统一格式化逻辑
        # 例如：将 1/4" 转换为标准格式
        size_str = size_str.replace('"', ' inch')
        size_str = size_str.replace("'", ' ft')

        # 移除多余空格
        size_str = ' '.join(size_str.split())

        return size_str

    def _col_num_to_letter(self, col_num: int) -> str:
        """
        将列号转换为Excel列字母

        Args:
            col_num: 列号（从1开始）

        Returns:
            Excel列字母（如A, B, AA等）
        """
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result

    def _format_size_with_symbol(self, size: str) -> str:
        """
        智能格式化尺寸，使用符号"显示（专用于FBA子体Size字段）

        Args:
            size: 原始尺寸字符串

        Returns:
            格式化后的尺寸（inch改为符号"）
        """
        if not size or pd.isna(size):
            return ""

        size_str = str(size).strip()

        # 将 inch 改为符号 "
        size_str = size_str.replace(' inch', '"')
        size_str = size_str.replace('inch', '"')

        # 移除多余空格
        size_str = ' '.join(size_str.split())

        return size_str

    def _generate_smart_title(self, source_data: Dict[str, Any], keyword: str) -> str:
        """
        根据实际组合情况智能生成标题

        Args:
            source_data: 源数据
            keyword: 商品关键词

        Returns:
            智能生成的标题
        """
        # 获取基础信息
        size = source_data.get('size', '')
        color = source_data.get('color', '')
        brand = source_data.get('亚马逊品牌', '')

        # 智能组合标题
        title_parts = []

        # 添加品牌（如果有且不是GENERIC）
        if brand and brand.upper() != 'GENERIC':
            title_parts.append(brand)

        # 添加尺寸
        if size:
            formatted_size = self._format_size(size)
            title_parts.append(formatted_size)

        # 添加关键词
        if keyword:
            title_parts.append(keyword)

        # 添加颜色（如果有）
        if color:
            title_parts.append(f"({color})")

        # 组合标题
        if title_parts:
            return " ".join(title_parts)
        else:
            return f"FBA {size} {keyword}" if size and keyword else "FBA Product"

    def find_sheet_name(self, file_path: str, target_sheet_names: List[str]) -> Optional[str]:
        """
        智能查找工作表名称，支持大小写不敏感匹配
        
        Args:
            file_path: Excel文件路径
            target_sheet_names: 目标工作表名称列表（按优先级排序）
            
        Returns:
            找到的工作表名称，如果未找到返回None
        """
        try:
            excel_file = pd.ExcelFile(file_path)
            available_sheets = excel_file.sheet_names
            
            logger.info(f"文件 {os.path.basename(file_path)} 的工作表: {available_sheets}")
            
            # 遍历目标工作表名称列表
            for target_name in target_sheet_names:
                # 1. 精确匹配
                if target_name in available_sheets:
                    logger.info(f"找到精确匹配的工作表: {target_name}")
                    return target_name
                
                # 2. 大小写不敏感匹配
                for sheet_name in available_sheets:
                    if target_name.lower() == sheet_name.lower():
                        logger.info(f"找到大小写不敏感匹配的工作表: {sheet_name} (目标: {target_name})")
                        return sheet_name
                
                # 3. 部分匹配（包含关系）
                for sheet_name in available_sheets:
                    if target_name.lower() in sheet_name.lower() or sheet_name.lower() in target_name.lower():
                        logger.info(f"找到部分匹配的工作表: {sheet_name} (目标: {target_name})")
                        return sheet_name
            
            # 如果都没找到，返回第一个工作表
            if available_sheets:
                logger.warning(f"未找到匹配的工作表，使用第一个工作表: {available_sheets[0]}")
                return available_sheets[0]
            
            return None
            
        except Exception as e:
            log_exception(e, f"读取Excel文件工作表时出错 - 文件: {file_path}")
            return None
        
    def load_data_files(self, target_file_path: Optional[str] = None,
                       product_file_path: Optional[str] = None,
                       template_file_path: Optional[str] = None) -> bool:
        """
        加载所有必要的数据文件

        Args:
            target_file_path: 目标listing文件路径（可选）
            product_file_path: 产品资料文件路径（可选）
            template_file_path: 模板文件路径（可选）

        Returns:
            是否成功加载所有文件
        """
        try:
            # 确定文件路径 - 优先使用参数，然后使用商城配置，最后使用默认路径
            if target_file_path:
                target_file = target_file_path
            elif self.marketplace and self.marketplace.config.target_file:
                target_file = os.path.join(self.base_path, self.marketplace.config.target_file)
            else:
                target_file = os.path.join(self.base_path, 'data', 'input', '已上架低价商城listing.xlsx')

            logger.info(f"加载目标listing表格: {target_file}")

            # 检查文件是否存在
            if not os.path.exists(target_file):
                logger.warning(f"目标listing文件不存在: {target_file}")
                # 创建一个空的DataFrame作为占位符
                self.target_listing_df = pd.DataFrame()
            else:
                # 智能查找工作表名称
                target_sheet_names = ['sheet1', 'Sheet1', '工作表1', '数据', 'data', 'listing']
                target_sheet = self.find_sheet_name(target_file, target_sheet_names)

                if target_sheet:
                    self.target_listing_df = self.safe_read_excel(target_file, target_sheet)
                    logger.info(f"目标listing数据: {len(self.target_listing_df)}行")
                else:
                    logger.error(f"无法找到合适的工作表在文件: {target_file}")
                    self.target_listing_df = pd.DataFrame()

            # 确定产品资料文件路径
            if product_file_path:
                product_file = product_file_path
            elif self.marketplace and self.marketplace.config.product_file:
                product_file = os.path.join(self.base_path, self.marketplace.config.product_file)
            else:
                product_file = os.path.join(self.base_path, 'data', 'input', '产品资料库-丝带-20250416.xlsx')

            logger.info(f"加载产品资料表格: {product_file}")

            # 检查产品资料文件是否存在
            if not os.path.exists(product_file):
                logger.warning(f"产品资料文件不存在: {product_file}")
                self.product_data_df = pd.DataFrame()
            else:
                # 智能查找产品资料工作表
                product_sheet_names = ['单个产品', '产品', '产品资料', 'products', 'product']
                product_sheet = self.find_sheet_name(product_file, product_sheet_names)

                if product_sheet:
                    self.product_data_df = self.safe_read_excel(product_file, product_sheet)
                    logger.info(f"产品资料数据: {len(self.product_data_df)}行")
                else:
                    logger.error(f"无法找到合适的工作表在文件: {product_file}")
                    self.product_data_df = pd.DataFrame()

            # 确定模板文件路径
            if template_file_path:
                template_file = template_file_path
            elif self.marketplace and self.marketplace.config.template_file:
                template_file = os.path.join(self.base_path, self.marketplace.config.template_file)
            else:
                template_file = os.path.join(self.base_path, 'data', 'input', '低价商城template.xlsm')

            logger.info(f"加载亚马逊模板: {template_file}")

            # 检查模板文件是否存在
            if not os.path.exists(template_file):
                logger.warning(f"模板文件不存在: {template_file}，使用基本模板结构")
                # 创建一个基本的模板结构（根据商城类型调整）
                if self.marketplace and hasattr(self.marketplace, 'config') and self.marketplace.config.name == 'fba':
                    # FBA商城使用不同的字段结构
                    basic_columns = [
                        'Product Type', 'Seller SKU', 'Brand', 'Update Delete', 'Product ID', 'Product ID Type',
                        'Title', 'Product Description', 'Parentage', 'Parent SKU', 'Relationship Type',
                        'Variation Theme', 'Color', 'Size', 'Country/Region of Origin',
                        'Is this product a battery or does it utilize batteries?'
                    ]
                else:
                    # 其他商城使用标准字段结构
                    basic_columns = [
                        'Seller SKU', 'Record Action', 'Product Type', 'Item Name',
                        'Brand Name', 'Item Type Keyword', 'Product Description',
                        'Bullet Point', 'Color', 'Parentage Level', 'Child Relationship Type',
                        'Parent SKU', 'Variation Theme Name', 'Country of Origin',
                        'Are batteries required?', 'Dangerous Goods Regulations'
                    ]
                self.template_df = pd.DataFrame(columns=basic_columns)
                logger.info("使用基本模板结构")
            else:
                # 智能查找模板工作表
                template_sheet_names = ['Template', 'template', '模板', 'listing', 'data']
                template_sheet = self.find_sheet_name(template_file, template_sheet_names)

                if not template_sheet:
                    logger.warning(f"无法找到合适的工作表在文件: {template_file}，使用基本模板结构")
                    # 创建基本模板结构
                    basic_columns = [
                        'Seller SKU', 'Record Action', 'Product Type', 'Item Name',
                        'Brand Name', 'Item Type Keyword', 'Product Description',
                        'Bullet Point', 'Color', 'Parentage Level', 'Child Relationship Type',
                        'Parent SKU', 'Variation Theme Name', 'Country of Origin',
                        'Are batteries required?', 'Dangerous Goods Regulations'
                    ]
                    self.template_df = pd.DataFrame(columns=basic_columns)
                else:
                    # 使用安全读取方法读取模板文件
                    try:
                        # 使用商城配置的表头行
                        header_row = self.marketplace.get_template_header_row() - 1 if self.marketplace else 3
                        self.template_df = self.safe_read_excel(template_file, template_sheet, header=header_row)
                        logger.info(f"成功读取模板文件，工作表: {template_sheet}，表头行: {header_row + 1}")
                    except Exception as e:
                        log_exception(e, f"读取模板文件失败 - 文件: {template_file}")
                        logger.warning(f"读取模板文件失败: {e}，使用基本模板结构")
                        # 创建一个基本的模板结构
                        basic_columns = [
                            'Seller SKU', 'Record Action', 'Product Type', 'Item Name',
                            'Brand Name', 'Item Type Keyword', 'Product Description',
                            'Bullet Point', 'Color', 'Parentage Level', 'Child Relationship Type',
                            'Parent SKU', 'Variation Theme Name', 'Country of Origin',
                            'Are batteries required?', 'Dangerous Goods Regulations'
                        ]
                        self.template_df = pd.DataFrame(columns=basic_columns)
                        logger.info("使用基本模板结构")

            logger.info(f"模板列: {list(self.template_df.columns)}")
            return True
            
        except Exception as e:
            log_exception(e, "加载数据文件时出错")
            return False
    
    def validate_data(self) -> Tuple[bool, List[str]]:
        """
        验证数据完整性
        
        Returns:
            (是否验证通过, 错误信息列表)
        """
        errors = []
        
        # 检查目标listing表格必要字段
        required_target_fields = ['MSKU', 'SKU']
        for field in required_target_fields:
            if field not in self.target_listing_df.columns:
                errors.append(f"目标listing表格缺少必要字段: {field}")

        # 检查color和size字段，支持变体属性列作为替代
        has_color = 'color' in self.target_listing_df.columns
        has_size = 'size' in self.target_listing_df.columns
        has_variant_attrs = '变体属性' in self.target_listing_df.columns

        if not has_color and not has_variant_attrs:
            errors.append(f"目标listing表格缺少必要字段: color (或变体属性)")

        if not has_size and not has_variant_attrs:
            errors.append(f"目标listing表格缺少必要字段: size (或变体属性)")

        # 如果使用变体属性列，记录调试信息
        if has_variant_attrs and (not has_color or not has_size):
            from utils.debug_helper import log_debug
            log_debug("使用变体属性列替代color/size字段", {
                "有color列": has_color,
                "有size列": has_size,
                "有变体属性列": has_variant_attrs
            })
        
        # 检查产品资料表格必要字段
        required_product_fields = ['*SKU（必填）']
        for field in required_product_fields:
            if field not in self.product_data_df.columns:
                errors.append(f"产品资料表格缺少必要字段: {field}")
        
        # 检查模板必要字段（根据商城类型调整）
        if self.marketplace and hasattr(self.marketplace, 'config') and 'FBA' in self.marketplace.config.display_name:
            # FBA商城使用不同的字段名称
            required_template_fields = ['Seller SKU', 'Update Delete', 'Product Type']
        else:
            # 其他商城使用标准字段名称
            required_template_fields = ['Seller SKU', 'Record Action', 'Product Type']

        for field in required_template_fields:
            if field not in self.template_df.columns:
                errors.append(f"亚马逊模板缺少必要字段: {field}")
        
        return len(errors) == 0, errors
    
    def generate_skc_mapping(self) -> Dict[str, str]:
        """
        生成SKC映射关系
        通过目标listing的SKU字段匹配产品资料的SKC信息
        
        Returns:
            SKU到SKC的映射字典
        """
        skc_mapping = {}
        
        # 检查产品资料表中是否有SKC相关字段
        possible_skc_fields = ['SKC', 'skc', 'Skc', 'SKU码', 'sku码']
        skc_field = None
        
        for field in possible_skc_fields:
            if field in self.product_data_df.columns:
                skc_field = field
                break
        
        if skc_field:
            for _, row in self.product_data_df.iterrows():
                sku = row.get('*SKU（必填）')
                skc = row.get(skc_field)
                if pd.notna(sku) and pd.notna(skc):
                    skc_mapping[sku] = skc
        
        logger.info(f"生成SKC映射: {len(skc_mapping)}个SKU映射关系")
        return skc_mapping
    
    def detect_variant_attributes_column(self) -> Optional[str]:
        """
        检测目标表格中是否存在变体属性字段

        Returns:
            变体属性字段名，如果不存在则返回None
        """
        if self.target_listing_df is None or self.target_listing_df.empty:
            return None

        # 可能的变体属性字段名
        variant_column_names = ['变体属性', 'variant_attributes', 'attributes', '属性']

        for col_name in self.target_listing_df.columns:
            if col_name in variant_column_names:
                logger.info(f"🔍 检测到变体属性字段: {col_name}")
                return col_name

            # 检查字段内容是否符合变体属性格式
            if not self.target_listing_df[col_name].empty:
                sample_value = str(self.target_listing_df[col_name].iloc[0])
                if VariantAttributeParser.validate_variant_format(sample_value):
                    logger.info(f"🔍 检测到变体属性格式字段: {col_name}")
                    return col_name

        return None

    def _check_brand_consistency(self) -> str:
        """
        检查所有SKU的亚马逊品牌列是否一致

        Returns:
            统一的品牌名称

        Raises:
            Exception: 如果品牌名称不一致
        """
        # 优先检查target_listing_df，如果不存在再检查data
        data_to_check = None
        if hasattr(self, 'target_listing_df') and self.target_listing_df is not None and not self.target_listing_df.empty:
            data_to_check = self.target_listing_df
            logger.debug("使用target_listing_df检查品牌一致性")
        elif hasattr(self, 'data') and self.data is not None and not self.data.empty:
            data_to_check = self.data
            logger.debug("使用data检查品牌一致性")
        else:
            logger.warning("⚠️ 没有数据可检查品牌一致性")
            return ""

        # 检查是否存在亚马逊品牌列
        if '亚马逊品牌' not in data_to_check.columns:
            logger.warning("⚠️ 目标数据中没有找到'亚马逊品牌'列")
            return ""

        # 获取所有非空的品牌值
        brand_values = data_to_check['亚马逊品牌'].dropna().astype(str).str.strip()
        brand_values = brand_values[brand_values != '']  # 过滤空字符串

        if brand_values.empty:
            logger.warning("⚠️ 亚马逊品牌列中没有有效数据")
            return ""

        # 获取唯一品牌值
        unique_brands = brand_values.unique()

        if len(unique_brands) > 1:
            logger.error(f"❌ 品牌名称不一致！发现 {len(unique_brands)} 个不同的品牌:")
            for i, brand in enumerate(unique_brands, 1):
                logger.error(f"   {i}. '{brand}'")
            raise Exception(f"品牌名称不一致！目标表格中发现 {len(unique_brands)} 个不同的品牌名称: {list(unique_brands)}。请确保所有SKU的亚马逊品牌列名称保持一致。")

        unified_brand = unique_brands[0]
        logger.info(f"✅ 品牌一致性检查通过，统一品牌名称: '{unified_brand}'")
        return unified_brand

    def create_parent_child_structure(self, keyword: str, dimension: str = 'SKC') -> Tuple[List[Dict], List[Dict]]:
        """
        创建父子体结构

        Args:
            keyword: 商品关键词
            dimension: 父体维度 ('SKC', 'MATERIAL_WIDTH', 'MATERIAL_LENGTH', 'VARIANT_ATTRIBUTES')

        Returns:
            (父体数据列表, 子体数据列表)
        """
        # 检查品牌一致性并获取统一品牌名
        self.unified_brand = self._check_brand_consistency()
        logger.info(f"🏷️ 使用统一品牌名称: '{self.unified_brand}'")

        # 检测是否存在变体属性字段
        variant_column = self.detect_variant_attributes_column()
        if variant_column and dimension == 'SKC':
            logger.info("🎯 检测到变体属性字段，使用变体属性解析模式")
            return self._create_variant_attributes_structure(keyword, variant_column)

        # 根据维度选择不同的分组策略
        if dimension == 'SKC':
            return self._create_skc_structure(keyword)
        elif dimension == 'MATERIAL_LENGTH':
            return self._create_material_length_structure(keyword)
        elif dimension == 'MATERIAL_WIDTH':
            return self._create_material_width_structure(keyword)
        elif dimension == 'VARIANT_ATTRIBUTES':
            if variant_column:
                return self._create_variant_attributes_structure(keyword, variant_column)
            else:
                logger.warning("未找到变体属性字段，回退到SKC模式")
                return self._create_skc_structure(keyword)
        else:
            raise ValueError(f"不支持的父体维度: {dimension}")

    def _create_skc_structure(self, keyword: str) -> Tuple[List[Dict], List[Dict]]:
        """创建基于SKC的父子体结构"""
        skc_mapping = self.generate_skc_mapping()

        # 按SKC分组生成父体
        skc_groups = {}
        child_data = []

        for _, row in self.target_listing_df.iterrows():
            msku = row.get('MSKU')
            sku = row.get('SKU')
            color = row.get('color')
            size = row.get('size')
            brand = row.get('亚马逊品牌', '')
            asin = row.get('ASIN', '')
            variant_attr = row.get('变体属性', '')
            price = row.get('价格', '')
            quantity = row.get('quantity', '')

            if pd.isna(msku) or pd.isna(sku):
                continue

            # 获取SKC（如果有映射的话）
            skc = skc_mapping.get(sku, sku)  # 如果没有映射就使用SKU本身

            # 记录SKC分组信息
            if skc not in skc_groups:
                skc_groups[skc] = {
                    'skc': skc,
                    'size': size,
                    'children': []
                }

            # 添加子体数据
            child_item = {
                'Seller SKU': msku,
                'Color': color,
                'parent_skc': skc
            }
            # 使用商城的默认值，并转换字段名
            child_defaults = self.marketplace.get_default_values('child', 'partial')
            child_defaults_converted = self.convert_field_names(child_defaults)
            child_item.update(child_defaults_converted)

            # 应用字段映射逻辑，包含所有必要的源数据
            source_data = {
                'MSKU': msku,
                'color': color,
                'size': size,
                'SKC': skc,
                '亚马逊品牌': brand,
                'ASIN': asin,
                '变体属性': variant_attr,
                '价格': price,
                'quantity': quantity
            }
            child_item = self.apply_field_mapping(child_item, 'child', keyword, source_data)
            child_data.append(child_item)

            # 记录到SKC分组
            skc_groups[skc]['children'].append(child_item)

        # 生成父体数据，同时保持SKC关联关系
        parent_data = []
        for skc, group_info in skc_groups.items():
            size = group_info.get('size', '')

            # 从对应的目标listing数据中获取共同的数据（如品牌等）
            # 找到第一个匹配的行来获取品牌等信息
            sample_row = None
            for _, row in self.target_listing_df.iterrows():
                row_sku = row.get('SKU')
                if skc_mapping.get(row_sku, row_sku) == skc:
                    sample_row = row
                    break

            parent_item = {
                'Seller SKU': '',  # 将通过字段映射生成
                '_original_skc': skc  # 保存原始SKC用于关联
            }
            # 使用商城的默认值，并转换字段名
            parent_defaults = self.marketplace.get_default_values('parent', 'partial')
            parent_defaults_converted = self.convert_field_names(parent_defaults)
            parent_item.update(parent_defaults_converted)

            # 应用字段映射逻辑，包含所有必要的源数据
            source_data = {
                'SKC': skc,
                'size': size,
                'children': group_info['children'],
                '亚马逊品牌': sample_row.get('亚马逊品牌', '') if sample_row is not None else '',
                '变体属性': sample_row.get('变体属性', '') if sample_row is not None else '',
                'color': sample_row.get('color', '') if sample_row is not None else '',
            }
            parent_item = self.apply_field_mapping(parent_item, 'parent', keyword, source_data)
            parent_data.append(parent_item)

        # 设置子体的Parent SKU
        for child_item in child_data:
            parent_skc = child_item.get('parent_skc')
            if parent_skc:
                # 找到对应的父体
                parent_item = next((p for p in parent_data if p.get('_original_skc') == parent_skc), None)
                if parent_item:
                    child_item['Parent SKU'] = parent_item['Seller SKU']
                else:
                    # 如果找不到对应的父体，使用parent_skc作为Parent SKU
                    child_item['Parent SKU'] = parent_skc

        logger.info(f"生成SKC结构: {len(parent_data)}个父体, {len(child_data)}个子体")
        return parent_data, child_data

    def _create_material_length_structure(self, keyword: str) -> Tuple[List[Dict], List[Dict]]:
        """创建基于材质+长度的父子体结构（相同材质+相同长度+不同宽度）"""
        # 按材质+长度分组生成父体
        dimension_groups = {}
        child_data = []

        # 获取用户选择的宽度信息
        selected_widths = None
        target_material = None
        target_length = None

        if hasattr(self, 'width_selection_info') and self.width_selection_info:
            selected_widths = set(self.width_selection_info.get('selected_widths', []))
            target_material = self.width_selection_info.get('material')
            target_length = self.width_selection_info.get('length')
            logger.info(f"🎯 使用用户选择的宽度: {len(selected_widths)}个宽度规格")

        for _, row in self.target_listing_df.iterrows():
            msku = row.get('MSKU')
            sku = row.get('SKU')

            # 尝试从多个可能的列中获取color和size信息
            color = ''
            size = ''
            product_description = ''

            # 优先从color和size列获取
            if 'color' in self.target_listing_df.columns:
                color = row.get('color', '')
            if 'size' in self.target_listing_df.columns:
                size = row.get('size', '')

            # 如果没有直接的color/size列，尝试从变体属性中提取
            if (not color or not size) and '变体属性' in self.target_listing_df.columns:
                variant_attrs = row.get('变体属性', '')
                if variant_attrs and isinstance(variant_attrs, str):
                    import re
                    # 提取color信息
                    if not color:
                        color_match = re.search(r'\[color:([^\]]+)\]', variant_attrs)
                        if color_match:
                            color = color_match.group(1).strip()

                    # 提取size信息
                    if not size:
                        size_match = re.search(r'\[size:([^\]]+)\]', variant_attrs)
                        if size_match:
                            size = size_match.group(1).strip()

            # 尝试从多个可能的列获取产品描述
            for desc_col in ['Product Description', '标题', '品名']:
                if desc_col in self.target_listing_df.columns:
                    desc_value = row.get(desc_col, '')
                    if desc_value and not pd.isna(desc_value):
                        product_description = str(desc_value)
                        break

            if pd.isna(msku) or pd.isna(sku):
                continue

            # 如果仍然没有size信息，跳过这行
            if not size or pd.isna(size):
                continue

            # 如果用户选择了特定宽度，只处理选中的宽度
            if selected_widths:
                size_str = str(size) if size else ''
                # 标准化比较：统一转换为小写并处理空格
                size_normalized = size_str.lower().replace(' ', '')
                selected_widths_normalized = [w.lower().replace(' ', '') for w in selected_widths]

                # 检查是否匹配
                if not any(size_normalized == w_norm for w_norm in selected_widths_normalized):
                    continue

            # 解析尺寸信息
            size_components = SizeParser.parse_size_components(str(size) if size else '')

            # 解析材质信息
            material = SizeParser.parse_material(str(product_description) if product_description else '', str(size) if size else '')
            if not material:
                material = "UNKNOWN"

            # 如果用户选择了特定材质，只处理匹配的材质
            if target_material and material != target_material:
                continue

            # 生成维度分组键（材质+长度）
            length_info = size_components.get('length_info')
            if length_info:
                dimension_key = f"{material}-{length_info['length_value']}{length_info['length_unit']}"

                # 如果用户选择了特定长度，只处理匹配的长度
                if target_length and length_info['length_display'] != target_length:
                    continue
            else:
                dimension_key = f"{material}-UNKNOWN_LENGTH"

            # 记录维度分组信息
            if dimension_key not in dimension_groups:
                dimension_groups[dimension_key] = {
                    'material': material,
                    'length_info': length_info,
                    'size': size,
                    'children': []
                }

            # 添加子体数据
            child_item = {
                'Seller SKU': msku,
                'Color': color,
                'parent_dimension_key': dimension_key
            }
            # 使用商城的默认值，并转换字段名
            child_defaults = self.marketplace.get_default_values('child', 'partial')
            child_defaults_converted = self.convert_field_names(child_defaults)
            child_item.update(child_defaults_converted)

            # 应用字段映射逻辑
            source_data = {'MSKU': msku, 'color': color, 'size': size, 'material': material}
            child_item = self.apply_field_mapping(child_item, 'child', keyword, source_data)
            child_data.append(child_item)

            # 记录到维度分组
            dimension_groups[dimension_key]['children'].append(child_item)

        # 生成父体数据
        parent_data = []
        for dimension_key, group_info in dimension_groups.items():
            size = group_info.get('size', '')
            material = group_info.get('material', 'UNKNOWN')
            length_info = group_info.get('length_info')

            # 使用商城的商品名称生成逻辑
            item_name = self.marketplace.generate_item_name(size, keyword)

            # 使用商城的父体SKU生成逻辑
            group_data = {
                'material': material,
                'length': length_info['length_display'] if length_info else 'UNKNOWN',
                'children': group_info['children']
            }
            parent_sku = self.marketplace.generate_parent_sku(group_data, 'MATERIAL_LENGTH')

            parent_item = {
                'Seller SKU': parent_sku,
                '_original_dimension_key': dimension_key  # 保存原始维度键用于关联
            }
            # 使用商城的默认值，并转换字段名
            parent_defaults = self.marketplace.get_default_values('parent', 'partial')
            parent_defaults_converted = self.convert_field_names(parent_defaults)
            parent_item.update(parent_defaults_converted)

            # 应用字段映射逻辑
            source_data = {'material': material, 'size': size, 'children': group_info['children']}
            parent_item = self.apply_field_mapping(parent_item, 'parent', keyword, source_data)
            parent_data.append(parent_item)

        # 设置子体的Parent SKU
        for child_item in child_data:
            parent_dimension_key = child_item.get('parent_dimension_key')
            if parent_dimension_key:
                # 找到对应的父体
                parent_item = next((p for p in parent_data if p.get('_original_dimension_key') == parent_dimension_key), None)
                if parent_item:
                    child_item['Parent SKU'] = parent_item['Seller SKU']
                else:
                    # 如果找不到对应的父体，使用维度键作为Parent SKU
                    child_item['Parent SKU'] = parent_dimension_key

        logger.info(f"生成材质+长度结构: {len(parent_data)}个父体, {len(child_data)}个子体")
        return parent_data, child_data

    def _create_material_width_structure(self, keyword: str) -> Tuple[List[Dict], List[Dict]]:
        """创建基于材质+宽度的父子体结构（相同材质+相同宽度+不同长度）"""
        # 这个方法暂时返回空结果，可以后续实现
        logger.warning("材质+宽度维度暂未实现")
        return [], []

    def _create_variant_attributes_structure(self, keyword: str, variant_column: str) -> Tuple[List[Dict], List[Dict]]:
        """
        创建基于变体属性的父子体结构

        Args:
            keyword: 商品关键词
            variant_column: 变体属性字段名

        Returns:
            (父体数据列表, 子体数据列表)
        """
        logger.info(f"🎯 开始处理变体属性字段: {variant_column}")

        # 按尺寸分组生成父体
        size_groups = {}
        child_data = []

        for _, row in self.target_listing_df.iterrows():
            # 获取基本信息
            msku = row.get('MSKU')
            variant_text = row.get(variant_column, '')

            if pd.isna(msku) or pd.isna(variant_text) or not variant_text:
                continue

            # 解析变体属性
            try:
                variant_data = VariantAttributeParser.convert_to_standard_format(str(variant_text))
                if not variant_data:
                    logger.warning(f"⚠️ 无法解析变体属性: {variant_text}")
                    continue

                size = variant_data.get('size', '')
                color = variant_data.get('color', '')

                if not size:
                    logger.warning(f"⚠️ 变体属性中缺少尺寸信息: {variant_text}")
                    continue

                # 按尺寸分组
                if size not in size_groups:
                    size_groups[size] = {
                        'size': size,
                        'size_info': variant_data,
                        'children': []
                    }

                # 创建子体数据
                child_item = {
                    'Seller SKU': msku,
                    'Color': color,
                    'parent_size': size
                }

                # 使用商城的默认值，并转换字段名
                child_defaults = self.marketplace.get_default_values('child', 'partial')
                child_defaults_converted = self.convert_field_names(child_defaults)
                child_item.update(child_defaults_converted)

                # 应用字段映射逻辑
                source_data = {
                    'MSKU': msku,
                    'color': color,
                    'size': size,
                    'variant_data': variant_data,
                    'original_variant_text': variant_text
                }
                child_item = self.apply_field_mapping(child_item, 'child', keyword, source_data)
                child_data.append(child_item)

                # 记录到尺寸分组
                size_groups[size]['children'].append(child_item)

            except Exception as e:
                logger.error(f"❌ 处理变体属性时出错: {e}, 变体文本: {variant_text}")
                logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
                import traceback
                logger.error(f"错误堆栈: {traceback.format_exc()}")
                print(f"❌ 处理变体属性时出错: {e}, 变体文本: {variant_text}")
                print(f"详细错误信息: {type(e).__name__}: {str(e)}")
                print(f"错误堆栈: {traceback.format_exc()}")
                continue

        # 生成父体数据
        parent_data = []
        for size, group_info in size_groups.items():
            if not group_info['children']:
                continue

            # 生成父体SKU
            parent_sku = self.marketplace.generate_parent_sku({'size': size}, 'SIZE')

            # 创建父体记录
            parent_item = {
                'Seller SKU': parent_sku,
                'size': size
            }

            # 使用商城的默认值，并转换字段名
            parent_defaults = self.marketplace.get_default_values('parent', 'partial')
            parent_defaults_converted = self.convert_field_names(parent_defaults)
            parent_item.update(parent_defaults_converted)

            # 应用字段映射逻辑
            source_data = {
                'size': size,
                'size_info': group_info['size_info'],
                'children_count': len(group_info['children'])
            }
            parent_item = self.apply_field_mapping(parent_item, 'parent', keyword, source_data)
            parent_data.append(parent_item)

            # 更新子体的父SKU
            for child in group_info['children']:
                child['Parent SKU'] = parent_sku

        logger.info(f"✅ 生成变体属性结构: {len(parent_data)}个父体, {len(child_data)}个子体")
        return parent_data, child_data
    
    def create_output_dataframe(self, parent_data: List[Dict], child_data: List[Dict]) -> pd.DataFrame:
        """
        创建输出数据框

        Args:
            parent_data: 父体数据列表
            child_data: 子体数据列表

        Returns:
            合并后的数据框
        """
        # 按SKC分组处理数据
        all_data = []

        # 按SKC或dimension_key分组子体数据
        skc_groups = {}
        for child_item in child_data:
            # 优先使用parent_skc，如果没有则使用parent_dimension_key
            skc = child_item.get('parent_skc') or child_item.get('parent_dimension_key')
            if skc:
                if skc not in skc_groups:
                    skc_groups[skc] = []
                skc_groups[skc].append(child_item)

        # 为每个父体添加对应的子体
        for parent_item in parent_data:
            # 获取父体的原始SKC或dimension_key
            original_skc = parent_item.get('_original_skc') or parent_item.get('_original_dimension_key')
            parent_sku = parent_item['Seller SKU']

            # 创建父体副本并移除临时字段
            parent_copy = parent_item.copy()
            parent_copy.pop('_original_skc', None)
            parent_copy.pop('_original_dimension_key', None)
            all_data.append(parent_copy)

            # 查找匹配的子体数据
            if original_skc and original_skc in skc_groups:
                children = skc_groups[original_skc]
                for child_item in children:
                    child_copy = child_item.copy()
                    child_copy['Parent SKU'] = parent_sku
                    child_copy.pop('parent_skc', None)  # 移除临时字段
                    child_copy.pop('parent_dimension_key', None)  # 移除临时字段
                    all_data.append(child_copy)
        
        # 创建数据框
        if all_data:
            output_df = pd.DataFrame(all_data)
            
            # 确保列顺序符合模板要求
            template_columns = self.template_df.columns.tolist()
            output_columns = []
            
            # 首先添加已有的列
            for col in template_columns:
                if col in output_df.columns:
                    output_columns.append(col)
            
            # 添加新的列
            for col in output_df.columns:
                if col not in output_columns:
                    output_columns.append(col)
            
            output_df = output_df.reindex(columns=output_columns)
        else:
            output_df = pd.DataFrame(columns=self.template_df.columns)
        
        return output_df
    
    def save_with_template_format(self, data_df: pd.DataFrame, output_file: str):
        """
        保存数据到Excel文件，完全保持原始模板一模一样，只填充Template工作表数据

        Args:
            data_df: 要保存的数据框
            output_file: 输出文件路径
        """
        # 根据当前商城类型选择正确的模板文件
        if self.marketplace and self.marketplace.config.template_file:
            template_file_path = os.path.join(self.base_path, self.marketplace.config.template_file)
        else:
            # 默认使用低价商城模板
            template_file_path = os.path.join(self.base_path, 'data', 'input', '低价商城template.xlsm')

        # 检查模板文件是否存在
        if not os.path.exists(template_file_path):
            logger.warning("原始模板文件不存在，使用标准保存方式")
            self.save_standard_format(data_df, output_file)
            return

        # 检测FBA模板，必须使用xlwings/COM方案
        if 'DECORATIVE_RIBBON_TRIM_US' in template_file_path:
            logger.info("🔧 检测到FBA模板，必须使用Excel COM接口...")
            try:
                self._save_with_xlwings(data_df, output_file, template_file_path)
                return
            except Exception as xlwings_error:
                logger.error(f"❌ Excel COM接口处理失败: {xlwings_error}")
                raise Exception(f"FBA模板处理失败，必须使用Excel COM接口: {xlwings_error}")

        # 低价商城等其他模板使用openpyxl方式
        try:
            logger.info("📋 使用openpyxl处理其他模板...")

            from openpyxl import load_workbook
            import shutil

            # 直接完整复制原始模板文件
            logger.info(f"复制原始模板文件: {os.path.basename(template_file_path)} -> {os.path.basename(output_file)}")
            try:
                shutil.copy2(template_file_path, output_file)
                logger.info("✅ 模板文件复制完成")
            except Exception as copy_error:
                logger.error(f"复制模板文件失败: {copy_error}")
                self.save_standard_format(data_df, output_file)
                return
            
            # 以保持完整格式的方式加载
            logger.info("📖 加载工作簿...")
            try:
                import time
                start_time = time.time()

                # 对于大型模板文件，尝试不同的加载策略
                logger.info("🔧 尝试加载工作簿...")

                # 首先尝试标准加载
                try:
                    wb = load_workbook(output_file, keep_vba=True)
                    load_time = time.time() - start_time
                    logger.info(f"✅ 工作簿加载完成 (耗时: {load_time:.2f}秒)")
                except Exception as first_error:
                    logger.warning(f"标准加载失败: {first_error}")
                    logger.info("🔄 尝试不保留VBA的方式加载...")

                    # 如果标准加载失败，尝试不保留VBA
                    wb = load_workbook(output_file, keep_vba=False)
                    load_time = time.time() - start_time
                    logger.info(f"✅ 工作簿加载完成(无VBA) (耗时: {load_time:.2f}秒)")

            except Exception as load_error:
                logger.error(f"加载工作簿失败: {load_error}")
                logger.info("⬇️ 降级使用标准保存方式...")
                self.save_standard_format(data_df, output_file)
                return

            if 'Template' not in wb.sheetnames:
                logger.error("模板文件中没有Template工作表")
                wb.close()
                self.save_standard_format(data_df, output_file)
                return
                
            ws = wb['Template']
            logger.info(f"📊 Template工作表维度: {ws.max_row} x {ws.max_column}")
            logger.info(f"📂 保持所有工作表: {wb.sheetnames}")
            
            # 找到数据开始行（第7行，保持前6行完全不变）
            data_start_row = 7
            
            # 只清除第7行及以后的数据，完全保留前6行
            logger.info("🧹 只清除数据区域，保留所有表头和格式...")
            if ws.max_row >= data_start_row:
                for row in range(data_start_row, ws.max_row + 1):
                    for col in range(1, ws.max_column + 1):
                        cell = ws.cell(row, col)
                        cell.value = None
                        # 不删除行，只清除值，保持格式
            
            # 创建列名到列索引的映射（基于第4行的真实列名）
            logger.info("🗂️ 创建列名映射...")
            col_mapping = {}  # 单个列映射（第一个找到的）
            all_col_mapping = {}  # 所有列映射（支持多个同名字段）
            header_row = 4  # 第4行是真正的列名
            
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(header_row, col).value
                if cell_value and str(cell_value).strip():
                    col_name = str(cell_value).strip()
                    
                    # 保存第一个找到的列（用于单列映射）
                    if col_name not in col_mapping:
                        col_mapping[col_name] = col
                    
                    # 保存所有同名列（用于多列映射）
                    if col_name not in all_col_mapping:
                        all_col_mapping[col_name] = []
                    all_col_mapping[col_name].append(col)
            
            logger.info(f"✅ 成功映射{len(col_mapping)}个列，发现{sum(len(cols) for cols in all_col_mapping.values())}个总列位置")
            
            # 写入新数据到数据区域
            logger.info(f"📝 写入{len(data_df)}行数据到Template工作表...")
            rows_written = 0
            cells_written = 0
            
            for idx, row_data in data_df.iterrows():
                current_row = data_start_row + idx
                logger.info(f"📋 处理第{idx+1}行数据 -> 写入到第{current_row}行")
                
                row_cells_written = 0
                for col_name, value in row_data.items():
                    if pd.isna(value) or value == '':
                        continue
                    
                    col_name_clean = str(col_name).strip()
                    
                    # 特殊处理：Dangerous Goods Regulations - 填充所有同名字段
                    if col_name_clean == 'Dangerous Goods Regulations':
                        target_cols = all_col_mapping.get(col_name_clean, [])
                        if target_cols:
                            for col_position in target_cols:
                                try:
                                    cell = ws.cell(current_row, col_position)
                                    cell.value = value
                                    cells_written += 1
                                    row_cells_written += 1
                                except Exception as e:
                                    logger.warning(f"写入失败 ({current_row}, {col_position}): {e}")
                        else:
                            logger.warning(f"⚠️ 列映射失败: {col_name_clean}")
                    
                    # 特殊处理：Product Description 和 Bullet Point - 只填充第一个字段
                    elif col_name_clean in ['Product Description', 'Bullet Point']:
                        col_position = col_mapping.get(col_name_clean)
                        if col_position:
                            try:
                                cell = ws.cell(current_row, col_position)
                                cell.value = value
                                cells_written += 1
                                row_cells_written += 1
                            except Exception as e:
                                logger.warning(f"写入失败 ({current_row}, {col_position}): {e}")
                        else:
                            logger.warning(f"⚠️ 列映射失败: {col_name_clean}")
                    
                    # 普通字段处理
                    else:
                        col_position = col_mapping.get(col_name_clean)
                        if col_position:
                            try:
                                cell = ws.cell(current_row, col_position)
                                cell.value = value
                                cells_written += 1
                                row_cells_written += 1
                            except Exception as e:
                                logger.warning(f"写入失败 ({current_row}, {col_position}): {e}")
                        else:
                            logger.warning(f"⚠️ 列映射失败: {col_name_clean}")
                
                logger.info(f"  第{current_row}行写入了{row_cells_written}个单元格")
                rows_written += 1
            
            logger.info(f"✅ 总共写入{rows_written}行，{cells_written}个单元格")
            
            # 保存文件（保持原始格式）
            logger.info("💾 保存文件，保持原始模板的所有特性...")
            wb.save(output_file)
            wb.close()
            
            logger.info(f"✅ 成功生成与原模板一模一样的文件，已填充{rows_written}行数据")
            
        except Exception as e:
            logger.error(f"❌ 保持模板格式失败: {e}")
            logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            print(f"❌ 保持模板格式失败: {e}")
            print(f"详细错误信息: {type(e).__name__}: {str(e)}")
            print(f"错误堆栈: {traceback.format_exc()}")

            # 如果是FBA模板，不允许降级，直接抛出异常
            if 'DECORATIVE_RIBBON_TRIM_US' in template_file_path:
                raise Exception(f"FBA模板处理失败，必须使用Excel COM接口: {e}")

            # 非FBA模板可以尝试使用xlwings方案
            logger.info("🔄 尝试使用xlwings方案...")
            try:
                self._save_with_xlwings(data_df, output_file, template_file_path)
                return
            except Exception as xlwings_error:
                logger.error(f"xlwings方案也失败: {xlwings_error}")
                logger.info("⬇️ 降级使用标准保存方式...")
                self.save_standard_format(data_df, output_file)

    def _save_with_xlwings(self, data_df: pd.DataFrame, output_file: str, template_file_path: str):
        """
        使用Excel COM接口处理VBA模板文件（不依赖xlwings）

        Args:
            data_df: 要保存的数据框
            output_file: 输出文件路径
            template_file_path: 模板文件路径
        """
        try:
            logger.info("📋 使用Excel COM接口处理VBA模板...")

            # 复制模板文件
            import shutil
            shutil.copy2(template_file_path, output_file)
            logger.info("✅ 模板文件复制完成")

            # 使用win32com操作Excel
            try:
                import win32com.client as win32
                import pythoncom
                logger.info("📖 使用COM接口打开Excel...")

                # 先尝试终止可能存在的Excel进程
                try:
                    import subprocess
                    subprocess.run(['taskkill', '/f', '/im', 'EXCEL.EXE'],
                                 capture_output=True, check=False)
                    import time
                    time.sleep(2)  # 等待进程完全终止
                except:
                    pass

                # 初始化COM
                pythoncom.CoInitialize()

                try:
                    # 创建Excel应用 - 使用最稳定的方式
                    excel_app = win32.DispatchEx("Excel.Application")
                    logger.info("✅ Excel应用创建成功")

                    # 设置基本属性
                    excel_app.Visible = False
                    excel_app.DisplayAlerts = False
                    excel_app.ScreenUpdating = False
                    logger.info("✅ Excel属性设置成功")

                except Exception as init_error:
                    logger.error(f"Excel初始化失败: {init_error}")
                    pythoncom.CoUninitialize()
                    raise

                try:
                    # 打开工作簿
                    wb = excel_app.Workbooks.Open(os.path.abspath(output_file))
                    logger.info("✅ Excel工作簿打开成功")

                    # 获取Template工作表
                    ws = wb.Worksheets("Template")

                    # 获取数据起始行
                    start_row = self.marketplace.get_template_start_row()['parent']

                    # FBA模板数据从第4行开始填充
                    start_row = 4
                    logger.info(f"🧹 清除现有数据，从第{start_row}行开始...")
                    clear_range = ws.Range(f"{start_row}:{start_row + 1000}")
                    clear_range.ClearContents()

                    # 创建字段名到列索引的映射（基于第2行的字段名）
                    logger.info("🗂️ 创建字段名映射...")
                    col_mapping = {}
                    header_row = 2  # FBA模板第2行是字段名

                    # 获取最大列数
                    max_col = ws.UsedRange.Columns.Count

                    for col in range(1, max_col + 1):
                        try:
                            cell_value = ws.Cells(header_row, col).Value
                            if cell_value and str(cell_value).strip():
                                col_name = str(cell_value).strip()
                                col_mapping[col_name] = col
                        except:
                            continue

                    logger.info(f"✅ 成功映射{len(col_mapping)}个字段")

                    # 按字段名批量写入数据（优化性能）
                    logger.info("📝 按字段名批量写入新数据...")
                    rows_written = 0
                    cells_written = 0

                    # 预处理数据，按列组织
                    column_data = {}
                    for col_name in data_df.columns:
                        col_name_clean = str(col_name).strip()
                        col_position = col_mapping.get(col_name_clean)

                        if col_position:
                            # 获取该列的所有非空值
                            col_values = []
                            for idx, value in enumerate(data_df[col_name]):
                                if pd.isna(value) or value == '':
                                    col_values.append('')
                                else:
                                    col_values.append(value)

                            if any(val != '' for val in col_values):  # 如果列中有非空值
                                column_data[col_position] = col_values
                        else:
                            logger.warning(f"⚠️ 字段映射失败: {col_name_clean}")

                    # 批量写入每列数据
                    total_rows = len(data_df)
                    for col_position, values in column_data.items():
                        try:
                            # 计算写入范围
                            end_row = start_row + total_rows - 1
                            range_address = f"{self._col_num_to_letter(col_position)}{start_row}:{self._col_num_to_letter(col_position)}{end_row}"

                            # 批量写入整列
                            ws.Range(range_address).Value = [[val] for val in values]
                            cells_written += len([v for v in values if v != ''])

                        except Exception as e:
                            logger.warning(f"批量写入失败 (列{col_position}): {e}")

                    rows_written = total_rows
                    logger.info(f"✅ 成功批量写入 {rows_written} 行 x {cells_written} 个单元格数据")

                    # 保存并关闭
                    wb.Save()
                    wb.Close()
                    logger.info("✅ Excel COM处理完成")

                finally:
                    # 确保关闭Excel应用
                    try:
                        excel_app.Quit()
                    except:
                        pass
                    # 清理COM
                    pythoncom.CoUninitialize()

            except ImportError:
                logger.error("❌ win32com库未安装，尝试安装: pip install pywin32")
                raise

        except Exception as e:
            logger.error(f"Excel COM处理失败: {e}")
            raise


    
    def save_standard_format(self, data_df: pd.DataFrame, output_file: str):
        """
        标准方式保存文件
        
        Args:
            data_df: 要保存的数据框
            output_file: 输出文件路径
        """
        logger.info("使用标准方式保存文件...")
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            data_df.to_excel(writer, sheet_name='Template', index=False)
    
    def fill_template(self, keyword: str, update_mode: str = 'partial', target_file_path: Optional[str] = None, dimension: str = 'SKC') -> str:
        """
        填充模板

        Args:
            keyword: 商品关键词
            update_mode: 更新模式 ('partial' 或 'full')
            target_file_path: 目标文件路径，用于确定输出位置
            dimension: 父体维度 ('SKC', 'MATERIAL_WIDTH', 'MATERIAL_LENGTH')

        Returns:
            生成的文件路径
        """
        logger.info(f"开始填充模板，关键词: {keyword}, 模式: {update_mode}, 维度: {dimension}")
        log_debug(f"填充模板参数", {
            "keyword": keyword,
            "update_mode": update_mode,
            "target_file_path": target_file_path,
            "dimension": dimension
        })

        # 创建父子体结构
        parent_data, child_data = self.create_parent_child_structure(keyword, dimension)
        
        # 确定输出位置
        if target_file_path and os.path.exists(target_file_path):
            # 使用目标文件的目录作为输出目录
            output_dir = os.path.dirname(target_file_path)
            logger.info(f"使用目标文件所在目录作为输出位置: {output_dir}")
        else:
            # 使用默认输出目录
            output_dir = os.path.join(self.base_path, 'data', 'output')
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"使用默认输出目录: {output_dir}")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # 清理关键词中的特殊字符，避免文件名问题
        safe_keyword = "".join(c for c in keyword if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_keyword = safe_keyword.replace(' ', '_')
        # 使用商城名称作为文件名前缀
        marketplace_name = self.marketplace.config.name
        output_file = os.path.join(output_dir, f"{marketplace_name}_listing_{safe_keyword}_{timestamp}.xlsm")
        
        # 创建输出数据框
        output_df = self.create_output_dataframe(parent_data, child_data)
        
        # 保存到Excel文件，保持原始格式
        try:
            self.save_with_template_format(output_df, output_file)
        except Exception as save_error:
            logger.error(f"使用模板格式保存失败: {save_error}")

            # 检查是否是FBA模板
            template_file_path = ""
            if self.marketplace and self.marketplace.config.template_file:
                template_file_path = os.path.join(self.base_path, self.marketplace.config.template_file)

            # 如果是FBA模板，不允许降级，直接抛出异常
            if 'DECORATIVE_RIBBON_TRIM_US' in template_file_path:
                logger.error("❌ FBA模板处理失败，不允许使用标准格式保存")
                raise save_error

            # 非FBA模板可以降级使用标准格式
            logger.info("⬇️ 使用标准格式保存...")
            standard_output_file = output_file.replace('.xlsm', '_standard.xlsx')
            self.save_standard_format(output_df, standard_output_file)
            output_file = standard_output_file
        
        logger.info(f"模板填充完成: {output_file}")
        return output_file
    
    def process_listing_creation(self, keyword: str,
                               target_file: Optional[str] = None,
                               product_file: Optional[str] = None,
                               template_file: Optional[str] = None,
                               update_mode: str = 'partial',
                               marketplace=None,
                               dimension: str = 'SKC',
                               width_selection_info: Optional[Dict] = None) -> Tuple[bool, str]:
        """
        处理listing创建的完整流程

        Args:
            keyword: 商品关键词
            target_file: 目标listing文件路径（可选）
            product_file: 产品资料文件路径（可选）
            template_file: 模板文件路径（可选）
            update_mode: 更新模式
            marketplace: 商城实例（可选，如果提供则覆盖初始化时的商城）
            dimension: 父体维度 ('SKC', 'MATERIAL_WIDTH', 'MATERIAL_LENGTH')
            width_selection_info: 宽度选择信息（用于MATERIAL_LENGTH维度）

        Returns:
            (是否成功, 结果信息)
        """
        # 如果提供了新的商城实例，使用它
        if marketplace:
            self.marketplace = marketplace

        # 保存宽度选择信息
        self.width_selection_info = width_selection_info
        try:
            # 如果提供了自定义文件路径，使用智能工作表名称匹配
            if target_file:
                logger.info(f"使用自定义目标listing文件: {target_file}")
                target_sheet_names = ['sheet1', 'Sheet1', '工作表1', '数据', 'data', 'listing']
                target_sheet = self.find_sheet_name(target_file, target_sheet_names)
                if target_sheet:
                    self.target_listing_df = self.safe_read_excel(target_file, target_sheet)
                    logger.info(f"成功读取目标listing文件，工作表: {target_sheet}")
                else:
                    return False, f"无法在文件 {target_file} 中找到合适的工作表"
                    
            if product_file:
                logger.info(f"使用自定义产品资料文件: {product_file}")
                product_sheet_names = ['单个产品', '产品', '产品资料', 'products', 'product']
                product_sheet = self.find_sheet_name(product_file, product_sheet_names)
                if product_sheet:
                    self.product_data_df = self.safe_read_excel(product_file, product_sheet)
                    logger.info(f"成功读取产品资料文件，工作表: {product_sheet}")
                else:
                    return False, f"无法在文件 {product_file} 中找到合适的工作表"
                    
            if template_file:
                logger.info(f"使用自定义模板文件: {template_file}")
                template_sheet_names = ['Template', 'template', '模板', 'listing', 'data']
                template_sheet = self.find_sheet_name(template_file, template_sheet_names)
                if template_sheet:
                    try:
                        # 使用商城配置的表头行
                        header_row = self.marketplace.get_template_header_row() - 1 if self.marketplace else 3
                        self.template_df = self.safe_read_excel(template_file, template_sheet, header=header_row)
                        logger.info(f"成功读取模板文件，工作表: {template_sheet}，表头行: {header_row + 1}")
                    except Exception as e:
                        logger.warning(f"读取模板文件失败: {e}，使用基本模板结构")
                        # 使用基本模板结构
                        basic_columns = [
                            'Seller SKU', 'Record Action', 'Product Type', 'Item Name',
                            'Brand Name', 'Item Type Keyword', 'Product Description', 
                            'Bullet Point', 'Color', 'Parentage Level', 'Child Relationship Type',
                            'Parent SKU', 'Variation Theme Name', 'Country of Origin',
                            'Are batteries required?', 'Dangerous Goods Regulations'
                        ]
                        self.template_df = pd.DataFrame(columns=basic_columns)
                else:
                    return False, f"无法在文件 {template_file} 中找到合适的工作表"
            
            # 加载缺失的默认文件（使用商城配置）
            if self.target_listing_df is None:
                logger.info(f"加载默认目标listing文件: {self.marketplace.config.display_name}")
                default_target_file = os.path.join(self.base_path, self.marketplace.config.target_file)
                target_sheet_names = ['sheet1', 'Sheet1', '工作表1', '数据', 'data', 'listing']
                target_sheet = self.find_sheet_name(default_target_file, target_sheet_names)
                if target_sheet:
                    logger.info(f"开始读取默认目标listing文件: {default_target_file}，工作表: {target_sheet}")
                    self.target_listing_df = self.safe_read_excel(default_target_file, target_sheet)
                    logger.info(f"成功加载默认目标listing文件，工作表: {target_sheet}，数据行数: {len(self.target_listing_df)}")
                else:
                    return False, f"无法加载默认目标listing文件: {default_target_file}"

            if self.product_data_df is None:
                logger.info(f"加载默认产品资料文件: {self.marketplace.config.display_name}")
                default_product_file = os.path.join(self.base_path, self.marketplace.config.product_file)
                product_sheet_names = ['单个产品', '产品', '产品资料', 'products', 'product']
                product_sheet = self.find_sheet_name(default_product_file, product_sheet_names)
                if product_sheet:
                    logger.info(f"开始读取默认产品资料文件: {default_product_file}，工作表: {product_sheet}")
                    self.product_data_df = self.safe_read_excel(default_product_file, product_sheet)
                    logger.info(f"成功加载默认产品资料文件，工作表: {product_sheet}，数据行数: {len(self.product_data_df)}")
                else:
                    return False, f"无法加载默认产品资料文件: {default_product_file}"

            if self.template_df is None:
                logger.info(f"加载默认模板文件: {self.marketplace.config.display_name}")
                default_template_file = os.path.join(self.base_path, self.marketplace.config.template_file)
                template_sheet_names = ['Template', 'template', '模板', 'listing', 'data']
                template_sheet = self.find_sheet_name(default_template_file, template_sheet_names)
                if template_sheet:
                    try:
                        # 使用商城配置的表头行
                        header_row = self.marketplace.get_template_header_row() - 1  # 转换为0基索引
                        self.template_df = self.safe_read_excel(default_template_file, template_sheet, header=header_row)
                        logger.info(f"成功加载默认模板文件，工作表: {template_sheet}，表头行: {header_row + 1}")
                    except Exception as e:
                        logger.warning(f"读取默认模板文件失败: {e}，使用基本模板结构")
                        basic_columns = [
                            'Seller SKU', 'Record Action', 'Product Type', 'Item Name',
                            'Brand Name', 'Item Type Keyword', 'Product Description',
                            'Bullet Point', 'Color', 'Parentage Level', 'Child Relationship Type',
                            'Parent SKU', 'Variation Theme Name', 'Country of Origin',
                            'Are batteries required?', 'Dangerous Goods Regulations'
                        ]
                        self.template_df = pd.DataFrame(columns=basic_columns)
                else:
                    return False, "无法加载默认模板文件"
            
            # 验证数据
            is_valid, errors = self.validate_data()
            if not is_valid:
                error_msg = "数据验证失败:\n" + "\n".join(errors)
                logger.error(error_msg)
                return False, error_msg
            
            # 填充模板
            output_file = self.fill_template(keyword, update_mode, target_file, dimension)
            
            success_msg = f"✅ 模板填充成功!\n输出文件: {output_file}"
            logger.info(success_msg)
            return True, success_msg
            
        except Exception as e:
            error_msg = f"处理过程中出错: {str(e)}"
            logger.error(error_msg)
            logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            print(f"❌ 处理过程中出错: {str(e)}")
            print(f"详细错误信息: {type(e).__name__}: {str(e)}")
            print(f"错误堆栈: {traceback.format_exc()}")
            return False, error_msg

    def safe_read_excel(self, file_path: str, sheet_name: str, **kwargs) -> pd.DataFrame:
        """
        安全读取Excel文件，处理各种数据类型和编码问题
        
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称
            **kwargs: 其他pandas.read_excel参数
            
        Returns:
            读取的DataFrame
        """
        try:
            # 方法1: 标准读取
            return pd.read_excel(file_path, sheet_name=sheet_name, **kwargs)
        except Exception as e1:
            logger.warning(f"标准读取失败: {e1}，尝试备用方法...")
            
            try:
                # 方法2: 强制所有列为字符串类型
                return pd.read_excel(file_path, sheet_name=sheet_name, dtype=str, **kwargs)
            except Exception as e2:
                logger.warning(f"字符串类型读取失败: {e2}，尝试分批读取...")
                
                try:
                    # 方法3: 分批读取（先读取小部分数据，然后逐步扩大）
                    # 先尝试读取100行
                    logger.info("尝试读取前100行数据...")
                    df_sample = pd.read_excel(file_path, sheet_name=sheet_name, nrows=100, **kwargs)
                    
                    # 如果成功，尝试读取更多
                    try:
                        logger.info("尝试读取前1000行数据...")
                        df_larger = pd.read_excel(file_path, sheet_name=sheet_name, nrows=1000, **kwargs)
                        return df_larger
                    except:
                        logger.warning("读取1000行失败，返回100行数据")
                        return df_sample
                        
                except Exception as e3:
                    logger.warning(f"分批读取失败: {e3}，尝试最小数据集...")
                    
                    try:
                        # 方法4: 只读取很少的行数（5-10行）
                        logger.info("尝试读取前10行数据作为最后手段...")
                        return pd.read_excel(file_path, sheet_name=sheet_name, nrows=10, **kwargs)
                    except Exception as e4:
                        logger.error(f"所有读取方法都失败了: {e4}")
                        raise e4


if __name__ == "__main__":
    # 测试模板填充功能
    filler = TemplateFiller()
    
    # 测试填充
    success, message = filler.process_listing_creation("satin ribbon")
    print(message) 