"""
Excel配置文件加载器
从Excel文件中加载字段映射配置，让程序小白也能轻松维护配置
"""

import pandas as pd
import os
from typing import Dict, Any, Optional
from loguru import logger


class ExcelConfigLoader:
    """Excel配置文件加载器"""
    
    def __init__(self, excel_file_path: Optional[str] = None):
        """
        初始化Excel配置加载器
        
        Args:
            excel_file_path: Excel配置文件路径，如果为None则使用默认路径
        """
        if excel_file_path is None:
            # 默认Excel配置文件路径
            current_dir = os.path.dirname(__file__)
            project_root = os.path.dirname(os.path.dirname(current_dir))
            excel_file_path = os.path.join(project_root, 'data', 'config', '字段映射配置.xlsx')
        
        self.excel_file_path = excel_file_path
        self._config_cache = None
        
    def _load_excel_config(self) -> Dict[str, Any]:
        """
        从Excel文件加载配置
        
        Returns:
            配置字典
        """
        if self._config_cache is not None:
            return self._config_cache
            
        try:
            if not os.path.exists(self.excel_file_path):
                logger.warning(f"Excel配置文件不存在: {self.excel_file_path}")
                return {}
            
            # 读取Excel文件的所有工作表
            excel_data = pd.read_excel(self.excel_file_path, sheet_name=None)
            
            config = {
                'marketplaces': {},
                'version': '1.0.0-excel',
                'last_updated': 'Excel文件',
                'source': 'Excel配置文件'
            }
            
            # 处理FBA商城配置
            if 'FBA商城字段映射' in excel_data:
                fba_df = excel_data['FBA商城字段映射']
                config['marketplaces']['fba'] = self._process_marketplace_sheet(fba_df, 'FBA商城')
            
            # 处理低价商城配置
            if '低价商城字段映射' in excel_data:
                low_price_df = excel_data['低价商城字段映射']
                config['marketplaces']['low_price'] = self._process_marketplace_sheet(low_price_df, '低价商城')
            
            self._config_cache = config
            logger.info(f"成功加载Excel字段映射配置: {self.excel_file_path}")
            return config
            
        except Exception as e:
            logger.error(f"加载Excel配置文件失败: {e}")
            return {}
    
    def _process_marketplace_sheet(self, df: pd.DataFrame, marketplace_name: str) -> Dict[str, Any]:
        """
        处理商城工作表数据
        
        Args:
            df: 工作表数据
            marketplace_name: 商城名称
            
        Returns:
            商城配置字典
        """
        marketplace_config = {
            'name': marketplace_name,
            'description': f'{marketplace_name}字段映射配置 - 来自Excel文件',
            'field_mappings': {
                'partial_update': {}
            },
            'default_values': {
                'parent': {},
                'child': {}
            }
        }
        
        # 处理每一行字段配置
        for _, row in df.iterrows():
            try:
                # 尝试新格式的列名
                field_name = str(row.get('程序字段名', '')).strip()
                if not field_name or field_name == 'nan':
                    # 如果没有新格式，尝试旧格式
                    field_name = str(row.get('字段名', '')).strip()
                    if not field_name or field_name == 'nan':
                        continue

                # 尝试新格式的列名
                parent_value = str(row.get('父体如何填充', '')).strip()
                child_value = str(row.get('子体如何填充', '')).strip()
                description = str(row.get('字段说明', '')).strip()

                # 如果没有新格式，尝试旧格式
                if parent_value == 'nan' or not parent_value:
                    parent_value = str(row.get('父体值', '')).strip()
                if child_value == 'nan' or not child_value:
                    child_value = str(row.get('子体值', '')).strip()
                if description == 'nan' or not description:
                    description = str(row.get('描述', '')).strip()

                # 处理NaN值
                if parent_value == 'nan':
                    parent_value = ''
                if child_value == 'nan':
                    child_value = ''
                if description == 'nan':
                    description = ''

                # 转换新格式的填充方式为程序可识别的格式
                parent_value = self._convert_fill_method(parent_value)
                child_value = self._convert_fill_method(child_value)
                
                # 构建字段映射
                if parent_value != child_value:
                    # 父体和子体值不同
                    field_mapping = {
                        'parent': parent_value,
                        'child': child_value
                    }
                else:
                    # 父体和子体值相同
                    field_mapping = parent_value if parent_value else child_value
                
                if description:
                    if isinstance(field_mapping, dict):
                        field_mapping['description'] = description
                    else:
                        field_mapping = {
                            'value': field_mapping,
                            'description': description
                        }
                
                marketplace_config['field_mappings']['partial_update'][field_name] = field_mapping
                
                # 添加到默认值配置
                if parent_value:
                    marketplace_config['default_values']['parent'][field_name] = parent_value
                if child_value:
                    marketplace_config['default_values']['child'][field_name] = child_value
                    
            except Exception as e:
                logger.warning(f"处理字段配置行时出错: {e}")
                continue
        
        return marketplace_config

    def _convert_fill_method(self, fill_method: str) -> str:
        """
        转换新格式的填充方式为程序可识别的格式

        Args:
            fill_method: 新格式的填充方式描述

        Returns:
            程序可识别的格式
        """
        if not fill_method or fill_method.strip() == '':
            return ''

        fill_method = fill_method.strip()

        # 程序自动生成
        if '程序自动生成' in fill_method:
            return 'generated'

        # 固定填写
        if '固定填写→' in fill_method:
            return fill_method.replace('固定填写→', '')

        # 来自数据文件
        if '来自→' in fill_method and '→' in fill_method:
            parts = fill_method.split('→')
            if len(parts) >= 3:
                # 格式: 来自→文件名→列名
                file_name = parts[1].strip()
                column_name = parts[2].replace('列', '').strip()

                # 处理"目标listing表格（用户上传）"的情况
                if '目标listing表格' in file_name or '用户上传' in file_name:
                    return f'target_listing.{column_name}'
                else:
                    return f'target_listing.{column_name}'

        # 引用父体SKU
        if '引用父体' in fill_method or '引用父体的SKU' in fill_method:
            return 'generated_parent_sku'

        # 不填充
        if '不填充' in fill_method or '留空' in fill_method:
            return ''

        # 其他情况直接返回原值
        return fill_method
    
    def get_marketplace_config(self, marketplace_type: str) -> Dict[str, Any]:
        """
        获取指定商城的配置
        
        Args:
            marketplace_type: 商城类型 ('low_price' 或 'fba')
            
        Returns:
            商城配置字典
        """
        config = self._load_excel_config()
        marketplaces = config.get('marketplaces', {})
        return marketplaces.get(marketplace_type, {})
    
    def get_field_mappings(self, marketplace_type: str, update_mode: str = 'partial_update') -> Dict[str, Any]:
        """
        获取字段映射关系
        
        Args:
            marketplace_type: 商城类型
            update_mode: 更新模式 ('partial_update' 或 'full_update')
            
        Returns:
            字段映射字典
        """
        marketplace_config = self.get_marketplace_config(marketplace_type)
        field_mappings = marketplace_config.get('field_mappings', {})
        return field_mappings.get(update_mode, {})
    
    def get_default_values(self, marketplace_type: str, record_type: str) -> Dict[str, Any]:
        """
        获取默认值配置
        
        Args:
            marketplace_type: 商城类型
            record_type: 记录类型 ('parent' 或 'child')
            
        Returns:
            默认值字典
        """
        marketplace_config = self.get_marketplace_config(marketplace_type)
        default_values = marketplace_config.get('default_values', {})
        return default_values.get(record_type, {})
    
    def reload_config(self):
        """重新加载配置文件"""
        self._config_cache = None
        logger.info("Excel配置缓存已清除，下次访问时将重新加载")
    
    def validate_excel_config(self) -> tuple[bool, list]:
        """
        验证Excel配置文件的完整性
        
        Returns:
            (是否验证通过, 错误信息列表)
        """
        errors = []
        
        if not os.path.exists(self.excel_file_path):
            errors.append(f"Excel配置文件不存在: {self.excel_file_path}")
            return False, errors
        
        try:
            # 尝试读取Excel文件
            excel_data = pd.read_excel(self.excel_file_path, sheet_name=None)
            
            # 检查必要的工作表
            required_sheets = ['FBA商城字段映射', '低价商城字段映射']
            for sheet_name in required_sheets:
                if sheet_name not in excel_data:
                    errors.append(f"缺少必要的工作表: {sheet_name}")
            
            # 检查工作表结构
            for sheet_name in required_sheets:
                if sheet_name in excel_data:
                    df = excel_data[sheet_name]
                    required_columns = ['字段名', '父体值', '子体值', '描述']
                    for col in required_columns:
                        if col not in df.columns:
                            errors.append(f"工作表 {sheet_name} 缺少必要的列: {col}")
            
        except Exception as e:
            errors.append(f"读取Excel文件时出错: {e}")
        
        return len(errors) == 0, errors
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Returns:
            配置摘要字典
        """
        config = self._load_excel_config()
        
        summary = {
            'version': config.get('version', 'unknown'),
            'source': config.get('source', 'unknown'),
            'marketplaces': [],
            'total_field_mappings': 0,
            'excel_file_path': self.excel_file_path
        }
        
        marketplaces = config.get('marketplaces', {})
        for marketplace_type, marketplace_config in marketplaces.items():
            marketplace_info = {
                'type': marketplace_type,
                'name': marketplace_config.get('name', marketplace_type),
                'description': marketplace_config.get('description', ''),
                'field_mappings_count': 0
            }
            
            field_mappings = marketplace_config.get('field_mappings', {})
            for update_mode, mappings in field_mappings.items():
                marketplace_info['field_mappings_count'] += len(mappings)
                summary['total_field_mappings'] += len(mappings)
            
            summary['marketplaces'].append(marketplace_info)
        
        return summary


# 全局Excel配置加载器实例
_excel_config_loader = None

def get_excel_config_loader() -> ExcelConfigLoader:
    """
    获取全局Excel配置加载器实例
    
    Returns:
        Excel配置加载器实例
    """
    global _excel_config_loader
    if _excel_config_loader is None:
        _excel_config_loader = ExcelConfigLoader()
    return _excel_config_loader
