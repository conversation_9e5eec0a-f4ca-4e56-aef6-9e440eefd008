"""
低价商城实现
继承自BaseMarketplace，实现低价商城特有的业务逻辑
"""

from typing import Dict, List, Any
from .base import BaseMarketplace, MarketplaceConfig
from .common_defaults import CommonDefaults, ValidationUtils, SKUGenerator
try:
    from ..config.field_mapping_loader import get_field_mapping_loader
except ImportError:
    # 处理相对导入问题
    import sys
    import os
    current_dir = os.path.dirname(__file__)
    parent_dir = os.path.dirname(current_dir)
    sys.path.insert(0, parent_dir)
    from config.field_mapping_loader import get_field_mapping_loader


class LowPriceMarketplace(BaseMarketplace):
    """低价商城实现类"""
    
    def __init__(self):
        """初始化低价商城配置"""
        config = MarketplaceConfig(
            name="low_price",
            display_name="🏪 低价商城",
            template_file="data/input/低价商城template.xlsm",
            product_file="data/input/产品资料库-丝带-20250416.xlsx",
            target_file="data/input/已上架低价商城listing.xlsx",
            supported_dimensions=["SKC", "MATERIAL_LENGTH", "VARIANT_ATTRIBUTES"],
            default_dimension="SKC",
            update_modes=["partial", "full"],
            default_update_mode="partial"
        )
        super().__init__(config)
    
    def get_field_mapping(self, update_mode: str = 'partial') -> Dict[str, Any]:
        """
        获取低价商城字段映射关系
        从配置文件中读取字段映射配置
        """
        try:
            # 从配置文件加载字段映射
            loader = get_field_mapping_loader()
            config_mode = 'partial_update' if update_mode == 'partial' else 'full_update'
            field_mappings = loader.get_field_mappings('low_price', config_mode)

            if field_mappings:
                # 转换配置格式为代码期望的格式
                converted_mappings = {}
                for field_name, field_config in field_mappings.items():
                    if isinstance(field_config, dict):
                        if 'parent' in field_config and 'child' in field_config:
                            # 父子体不同值的字段
                            converted_mappings[field_name] = {
                                'parent': field_config['parent'],
                                'child': field_config['child']
                            }
                        elif 'source' in field_config:
                            # 直接映射的字段
                            converted_mappings[field_name] = field_config['source']
                        elif 'value' in field_config:
                            # 固定值字段
                            converted_mappings[field_name] = field_config['value']

                return converted_mappings
        except Exception as e:
            from loguru import logger
            logger.warning(f"从配置文件加载字段映射失败，使用默认配置: {e}")

        # 如果配置文件加载失败，使用默认映射
        return self._get_default_field_mapping(update_mode)

    def _get_default_field_mapping(self, update_mode: str) -> Dict[str, Any]:
        """获取默认字段映射关系（配置文件加载失败时的备用方案）"""
        base_mapping = {
            # 基础映射关系
            'seller_sku': {
                'parent': 'generated',  # 动态生成
                'child': 'target_listing.MSKU'  # 直接映射
            },
            'record_action': {  # Record Action字段映射
                'parent': '',  # 父体Record Action为空
                'child': 'Partial Update'  # 子体Record Action
            },
            'product_description': 'target_listing.品名',
            'color': 'target_listing.color',
            'size': 'target_listing.size'
        }

        if update_mode == 'full':
            # 完全更新模式的额外映射
            base_mapping.update({
                'additional_field': 'some_mapping'
            })

        return base_mapping
    
    def get_default_values(self, record_type: str, update_mode: str = 'partial') -> Dict[str, Any]:
        """获取低价商城默认值配置"""
        if record_type == 'parent':
            return self._get_parent_defaults(update_mode)
        elif record_type == 'child':
            return self._get_child_defaults(update_mode)
        else:
            raise ValueError(f"不支持的记录类型: {record_type}")
    
    def _get_parent_defaults(self, update_mode: str) -> Dict[str, Any]:
        """
        获取父体默认值
        优先从配置文件加载，失败时使用代码中的默认值
        """
        try:
            # 从配置文件加载默认值
            loader = get_field_mapping_loader()
            config_defaults = loader.get_default_values('low_price', 'parent')

            if config_defaults:
                # 如果是完全更新模式，添加额外字段
                if update_mode == 'full':
                    config_defaults.update({
                        'Color': 'generated'  # 需要动态生成
                    })
                return config_defaults
        except Exception as e:
            from loguru import logger
            logger.warning(f"从配置文件加载父体默认值失败，使用代码默认值: {e}")

        # 配置文件加载失败时的备用方案
        defaults = CommonDefaults.get_parent_base_defaults()

        # 低价商城特有的默认值
        low_price_specific = {
            'record_action': '',  # 使用统一的字段名，父体为空
            'Product Type': 'HOME',
            'Parentage Level': 'Parent',
            'Child Relationship Type': 'Variation',
            'Variation Theme Name': 'COLOR',
            'Dangerous Goods Regulations': 'Not Applicable'
        }

        # 合并默认值
        defaults = CommonDefaults.merge_defaults(defaults, low_price_specific)

        if update_mode == 'full':
            # 完全更新模式的额外默认值
            defaults.update({
                'Color': 'generated'  # 需要动态生成
            })

        return defaults
    
    def _get_child_defaults(self, update_mode: str) -> Dict[str, Any]:
        """
        获取子体默认值
        优先从配置文件加载，失败时使用代码中的默认值
        """
        try:
            # 从配置文件加载默认值
            loader = get_field_mapping_loader()
            config_defaults = loader.get_default_values('low_price', 'child')

            if config_defaults:
                # 如果是完全更新模式，添加额外字段
                if update_mode == 'full':
                    config_defaults.update({
                        'Item Type Keyword': 'from_keyword',
                        'Product Description': 'generated',
                        'Bullet Point': 'generated'
                    })
                return config_defaults
        except Exception as e:
            from loguru import logger
            logger.warning(f"从配置文件加载子体默认值失败，使用代码默认值: {e}")

        # 配置文件加载失败时的备用方案
        defaults = CommonDefaults.get_child_base_defaults()

        # 低价商城特有的默认值
        low_price_specific = {
            'record_action': 'Partial Update',  # 使用统一的字段名
            'Product Type': 'HOME',
            'Parentage Level': 'Child',
            'Child Relationship Type': 'Variation',
            'Variation Theme Name': 'COLOR',
            'Dangerous Goods Regulations': 'Not Applicable'
        }

        # 合并默认值
        defaults = CommonDefaults.merge_defaults(defaults, low_price_specific)

        if update_mode == 'full':
            # 完全更新模式的额外默认值
            defaults.update({
                'Item Type Keyword': 'from_keyword',
                'Product Description': 'generated',
                'Bullet Point': 'generated'
            })

        return defaults
    
    def generate_parent_sku(self, group_data: Dict[str, Any], dimension: str) -> str:
        """
        生成低价商城父体SKU

        Args:
            group_data: 分组数据，包含SKC等信息
            dimension: 父体维度（支持SKC、MATERIAL_LENGTH、SIZE）

        Returns:
            生成的父体SKU
        """
        if dimension == "SKC":
            # 使用通用SKU生成器，低价商城不添加前缀
            return SKUGenerator.generate_basic_parent_sku(group_data, dimension, prefix="")

        elif dimension == "MATERIAL_LENGTH":
            # 材质+长度维度：基于材质和长度生成
            material = group_data.get('material', 'UNKNOWN')
            length = group_data.get('length', 'UNKNOWN')
            return f"{material}-{length}"

        elif dimension == "SIZE":
            # 尺寸维度：基于尺寸信息生成父体SKU（用于变体属性）
            size = group_data.get('size', 'UNKNOWN')
            # 清理尺寸字符串，移除特殊字符
            size_clean = size.replace('"', 'inch').replace('/', '-').replace(' ', '_').replace('X', 'x')
            return f"PARENT-{size_clean}"

        else:
            raise ValueError(f"不支持的父体维度: {dimension}")
    
    def generate_item_name(self, size: str, keyword: str) -> str:
        """
        生成低价商城商品名称

        Args:
            size: 尺寸信息
            keyword: 商品关键词

        Returns:
            商品名称
        """
        # 使用通用商品名称生成器，低价商城不添加前缀
        return SKUGenerator.generate_basic_item_name(size, keyword, prefix="")
    
    def validate_data(self, data: Dict[str, Any]) -> tuple[bool, str]:
        """
        验证低价商城数据

        Args:
            data: 待验证的数据

        Returns:
            (是否验证通过, 错误信息)
        """
        # 检查必要字段
        required_fields = ['MSKU', 'color']
        is_valid, message = ValidationUtils.validate_required_fields(data, required_fields)
        if not is_valid:
            return is_valid, message

        # 检查MSKU格式
        msku = data.get('MSKU', '')
        is_valid, message = ValidationUtils.validate_msku_format(msku)
        if not is_valid:
            return is_valid, message

        return True, "验证通过"
