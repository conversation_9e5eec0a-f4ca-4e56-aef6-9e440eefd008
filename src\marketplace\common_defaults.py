"""
商城通用默认值配置
提供各商城类型共享的默认值和工具函数，减少重复代码
"""

from typing import Dict, Any


class CommonDefaults:
    """商城通用默认值类"""
    
    # 通用的基础默认值
    COMMON_BASE_DEFAULTS = {
        'brand_name': 'GENERIC',
        'country_of_origin': 'China',
        'batteries_required': 'No',
        'relationship_type': 'Variation',
        'variation_theme': 'COLOR',
    }
    
    # 通用的父体默认值
    COMMON_PARENT_DEFAULTS = {
        'parent_child': 'Parent',
        'update_delete': 'Update',
    }
    
    # 通用的子体默认值
    COMMON_CHILD_DEFAULTS = {
        'parent_child': 'Child',
        'update_delete': 'PartialUpdate',
    }
    
    @classmethod
    def get_base_defaults(cls) -> Dict[str, Any]:
        """
        获取基础通用默认值
        
        Returns:
            基础默认值字典
        """
        return cls.COMMON_BASE_DEFAULTS.copy()
    
    @classmethod
    def get_parent_base_defaults(cls) -> Dict[str, Any]:
        """
        获取父体基础默认值
        
        Returns:
            父体基础默认值字典
        """
        defaults = cls.get_base_defaults()
        defaults.update(cls.COMMON_PARENT_DEFAULTS)
        return defaults
    
    @classmethod
    def get_child_base_defaults(cls) -> Dict[str, Any]:
        """
        获取子体基础默认值
        
        Returns:
            子体基础默认值字典
        """
        defaults = cls.get_base_defaults()
        defaults.update(cls.COMMON_CHILD_DEFAULTS)
        return defaults
    
    @classmethod
    def merge_defaults(cls, base_defaults: Dict[str, Any], 
                      specific_defaults: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并默认值配置
        
        Args:
            base_defaults: 基础默认值
            specific_defaults: 特定默认值
            
        Returns:
            合并后的默认值字典
        """
        merged = base_defaults.copy()
        merged.update(specific_defaults)
        return merged


class ValidationUtils:
    """数据验证工具类"""
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], 
                                required_fields: list) -> tuple[bool, str]:
        """
        验证必需字段
        
        Args:
            data: 待验证的数据
            required_fields: 必需字段列表
            
        Returns:
            (是否验证通过, 错误信息)
        """
        for field in required_fields:
            if field not in data or not data[field]:
                return False, f"缺少必要字段: {field}"
        return True, "验证通过"
    
    @staticmethod
    def validate_msku_format(msku: str) -> tuple[bool, str]:
        """
        验证MSKU格式
        
        Args:
            msku: MSKU值
            
        Returns:
            (是否验证通过, 错误信息)
        """
        if not isinstance(msku, str) or len(msku.strip()) == 0:
            return False, "MSKU格式不正确"
        return True, "MSKU格式正确"


class SKUGenerator:
    """SKU生成工具类"""
    
    @staticmethod
    def generate_basic_parent_sku(group_data: Dict[str, Any], 
                                 dimension: str, 
                                 prefix: str = "") -> str:
        """
        生成基础父体SKU
        
        Args:
            group_data: 分组数据
            dimension: 父体维度
            prefix: SKU前缀
            
        Returns:
            生成的父体SKU
        """
        if dimension == "SKC":
            skc = group_data.get('SKC', '')
            if skc:
                return f"{prefix}{skc}" if prefix else skc
            else:
                first_child = group_data.get('children', [{}])[0]
                child_sku = first_child.get('MSKU', 'UNKNOWN')
                return f"{prefix}{child_sku}" if prefix else child_sku
        else:
            raise ValueError(f"不支持的父体维度: {dimension}")
    
    @staticmethod
    def generate_basic_item_name(size: str, keyword: str, prefix: str = "") -> str:
        """
        生成基础商品名称
        
        Args:
            size: 尺寸信息
            keyword: 商品关键词
            prefix: 名称前缀
            
        Returns:
            商品名称
        """
        parts = []
        if prefix:
            parts.append(prefix)
        if size:
            parts.append(size)
        if keyword:
            parts.append(keyword)
        
        if parts:
            return " ".join(parts)
        else:
            return f"{prefix} Unknown Product" if prefix else "Unknown Product"
