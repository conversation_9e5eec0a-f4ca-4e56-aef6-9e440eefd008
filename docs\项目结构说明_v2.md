# 📁 亚马逊Listing创建工具 v2.0 - 项目结构说明

## 🎯 项目概述
本项目已升级为支持多种商城类型的亚马逊listing模板创建工具，采用模块化架构设计，支持低价商城、FBA商城等多种商城类型，具有良好的扩展性和维护性。

## 📂 项目目录结构

```
亚马逊Listing创建工具/
├── 📁 src/                              # 源代码目录
│   ├── 📁 marketplace/                  # 商城类型管理模块 (新增)
│   │   ├── __init__.py                  # 模块初始化
│   │   ├── base.py                      # 商城基类定义
│   │   ├── low_price.py                 # 低价商城实现
│   │   ├── fba.py                       # FBA商城实现
│   │   └── factory.py                   # 商城工厂类
│   ├── 📁 config/                       # 配置管理模块 (新增)
│   │   ├── __init__.py                  # 模块初始化
│   │   ├── settings.py                  # 全局设置配置
│   │   └── config_manager.py            # 配置管理器
│   ├── 📁 core/                         # 核心业务逻辑
│   │   ├── template_filler.py           # 重构后的模板填充核心类
│   │   ├── data_analyzer.py             # 数据分析工具
│   │   └── simple_test.py               # 简单测试脚本
│   ├── 📁 gui/                          # 图形用户界面
│   │   └── main_window.py               # 重构后的主窗口界面
│   ├── test_marketplace.py              # 架构测试脚本 (新增)
│   ├── main.py                          # 主程序入口
│   └── __init__.py                      # 包初始化文件
├── 📁 data/                             # 数据目录
│   ├── 📁 input/                        # 输入文件目录
│   │   ├── 已上架低价商城listing.xlsx    # 低价商城目标文件
│   │   ├── 已上架FBA商城listing.xlsx     # FBA商城目标文件 (待添加)
│   │   ├── 产品资料库-丝带-20250416.xlsx # 低价商城产品资料
│   │   ├── 产品资料库-FBA.xlsx           # FBA商城产品资料 (待添加)
│   │   ├── 低价商城template.xlsm         # 低价商城模板
│   │   └── FBA商城template.xlsm          # FBA商城模板 (待添加)
│   ├── 📁 output/                       # 输出文件目录
│   │   └── (生成的listing文件)
│   ├── 📁 logs/                         # 日志文件目录
│   │   └── listing_tool.log             # 应用程序日志
│   └── config.json                      # 用户配置文件 (自动生成)
├── 📁 docs/                             # 文档目录
│   ├── 多商城架构设计文档.md             # 架构设计文档 (新增)
│   ├── 低价商城listing创建工具需求文档.md # 原需求文档
│   ├── 字段映射关系.md                   # 字段映射说明
│   ├── 项目结构说明.md                   # 原结构说明
│   ├── 项目结构说明_v2.md                # 新结构说明
│   ├── 项目配置说明.md                   # 配置说明
│   └── README.md                        # 项目说明
├── 📁 scripts/                          # 脚本目录
│   └── 启动工具.py                       # 启动脚本
├── 📁 venv/                             # 虚拟环境目录
├── requirements.txt                     # 依赖包列表
└── README.md                           # 项目说明文件
```

## 🏗️ 架构设计说明

### 1. 商城类型管理模块 (marketplace/)
- **base.py**: 定义所有商城类型的通用接口和基础功能
- **low_price.py**: 低价商城的具体实现，保持原有业务逻辑
- **fba.py**: FBA商城的具体实现，支持FBA特有功能
- **factory.py**: 商城工厂类，统一管理商城实例的创建

### 2. 配置管理模块 (config/)
- **settings.py**: 全局设置，包括应用信息、路径配置、UI样式等
- **config_manager.py**: 配置管理器，处理用户配置、最近文件等

### 3. 核心业务逻辑 (core/)
- **template_filler.py**: 重构后的模板填充器，支持多商城类型
- **data_analyzer.py**: 数据分析工具
- **simple_test.py**: 简单测试脚本

### 4. 图形用户界面 (gui/)
- **main_window.py**: 重构后的主窗口，支持商城类型选择和切换

## 🔄 版本升级说明

### v1.0 → v2.0 主要变化

#### 新增功能
1. **多商城支持**: 支持低价商城和FBA商城
2. **商城类型选择**: GUI界面新增商城类型选择功能
3. **配置管理**: 统一的配置管理系统
4. **文件管理**: 支持三种文件类型的自定义选择
5. **最近文件**: 记录和快速访问最近使用的文件

#### 架构改进
1. **模块化设计**: 采用模块化架构，提高代码可维护性
2. **工厂模式**: 使用工厂模式管理商城实例
3. **配置分离**: 将配置从代码中分离，提高灵活性
4. **接口抽象**: 定义统一的商城接口，便于扩展

#### 向后兼容
1. **数据兼容**: 完全兼容原有的低价商城数据格式
2. **功能兼容**: 保持原有功能不变，新增功能为可选
3. **配置兼容**: 自动检测和转换旧配置

## 🚀 使用说明

### 启动方式
```bash
# 方式1: 直接运行主程序
python src/main.py

# 方式2: 使用启动脚本
python scripts/启动工具.py
```

### 测试架构
```bash
# 运行架构测试
python src/test_marketplace.py
```

### 基本使用流程
1. 启动应用程序
2. 选择商城类型（低价商城/FBA商城）
3. 输入商品关键词
4. 选择文件（可选，使用默认文件）
5. 配置更新模式和父体维度
6. 生成Listing模板

## 📊 商城类型对比

| 特性 | 低价商城 | FBA商城 |
|------|----------|---------|
| 状态 | ✅ 完全支持 | 🚧 架构就绪 |
| 模板文件 | 低价商城template.xlsm | FBA商城template.xlsm |
| 产品资料 | 产品资料库-丝带-20250416.xlsx | 产品资料库-FBA.xlsx |
| 支持维度 | SKC | SKC, 材质+宽度 |
| 更新模式 | 部分更新, 完全更新 | 部分更新, 完全更新 |
| 特有字段 | - | 履约中心ID, 库存数量 |

## 🔮 扩展指南

### 添加新商城类型
1. 在 `src/marketplace/` 目录下创建新的商城实现文件
2. 继承 `BaseMarketplace` 类并实现必要方法
3. 在 `MarketplaceFactory` 中注册新商城类型
4. 添加对应的配置和模板文件

### 自定义配置
1. 修改 `src/config/settings.py` 中的全局设置
2. 使用 `ConfigManager` 管理用户特定配置
3. 通过GUI界面的配置选项进行调整

## 🧪 测试说明

### 架构测试
- `src/test_marketplace.py`: 测试多商城架构的基本功能
- 验证商城工厂、配置管理、设置等核心组件

### 功能测试
- 通过GUI界面进行完整的功能测试
- 验证不同商城类型的模板生成功能

## 📞 技术支持

### 问题排查
1. 查看 `data/logs/listing_tool.log` 日志文件
2. 运行 `python src/test_marketplace.py` 检查架构
3. 检查 `data/config.json` 配置文件

### 常见问题
1. **导入错误**: 确保Python路径正确，运行测试脚本验证
2. **文件缺失**: 检查 `data/input/` 目录下的必要文件
3. **配置问题**: 删除 `data/config.json` 重置为默认配置
