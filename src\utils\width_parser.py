"""
尺寸解析工具
用于解析和识别各种宽度、长度格式的工具类
"""

import re
from typing import Optional, Dict, List
from fractions import Fraction
from loguru import logger


class SizeParser:
    """尺寸解析工具类，支持宽度和长度解析"""
    
    # 支持的宽度格式正则表达式（按优先级排序）
    WIDTH_PATTERNS = [
        # 新格式: 3/8 x 10 Yard, 5/8 x 25 Yard 等（宽度 x 长度 单位）
        (r'(\d+/\d+)[\s]*x[\s]*\d+[\s]*(?:yard|yards|yd)', 'fraction_x_length'),

        # 混合分数格式: 1-1/2", 2-3/4" 等
        (r'(\d+)-(\d+/\d+)[\s]*["\']?[\s]*(?:inch|inches|in)?', 'mixed_fraction'),

        # 混合分数+单位格式: 1-1/2 inch, 2-3/4 inches 等
        (r'(\d+)-(\d+/\d+)[\s]+(?:inch|inches|in)', 'mixed_fraction_unit'),

        # 分数格式: 1/4", 3/8", 5/8" 等
        (r'(\d+/\d+)[\s]*["\'][\s]*(?:inch|inches|in)?', 'fraction_quote'),

        # 分数+单位格式: 1/4 inch, 3/8 inches 等
        (r'(\d+/\d+)[\s]+(?:inch|inches|in)', 'fraction_unit'),

        # 小数格式: 1.5", 2.25" 等
        (r'(\d+\.\d+)[\s]*["\'][\s]*(?:inch|inches|in)?', 'decimal_quote'),

        # 整数格式: 1", 2", 3" 等
        (r'(\d+)[\s]*["\'][\s]*(?:inch|inches|in)?', 'integer_quote'),

        # 纯数字格式: 1 inch, 2 inches 等
        (r'(\d+(?:\.\d+)?)[\s]+(?:inch|inches|in)', 'number_unit'),
    ]
    
    @classmethod
    def parse_width(cls, size_text: str) -> Optional[Dict[str, any]]:
        """
        从尺寸文本中解析宽度信息
        
        Args:
            size_text: 尺寸文本，如 "1/4"*50 Yards", "3/8 inch ribbon" 等
            
        Returns:
            包含宽度信息的字典，如果无法解析则返回None
            {
                'width_value': float,  # 宽度数值（英寸）
                'width_text': str,     # 原始宽度文本
                'width_fraction': str, # 分数表示（如果适用）
                'width_display': str   # 显示格式
            }
        """
        if not size_text or not isinstance(size_text, str):
            return None
            
        size_text = size_text.strip()
        logger.debug(f"🔍 解析宽度文本: {size_text}")

        for pattern, pattern_type in cls.WIDTH_PATTERNS:
            match = re.search(pattern, size_text, re.IGNORECASE)
            if match:
                try:
                    width_info = cls._extract_width_from_match(match, pattern_type)
                    if width_info:
                        logger.debug(f"✅ 成功解析宽度: {width_info}")
                        return width_info
                except Exception as e:
                    logger.warning(f"⚠️ 解析宽度时出错: {e}")
                    continue
        
        logger.warning(f"❌ 无法解析宽度: {size_text}")
        return None
    
    @classmethod
    def _extract_width_from_match(cls, match, pattern_type: str) -> Optional[Dict[str, any]]:
        """
        从正则匹配结果中提取宽度信息

        Args:
            match: 正则匹配对象
            pattern_type: 模式类型

        Returns:
            宽度信息字典
        """
        groups = match.groups()
        width_text = match.group(0)

        try:
            # 处理混合分数格式 (如 1-1/2)
            if pattern_type in ['mixed_fraction', 'mixed_fraction_unit']:
                if len(groups) >= 2 and groups[1] and '/' in groups[1]:
                    whole_part = int(groups[0])
                    fraction_part = Fraction(groups[1])
                    width_value = float(whole_part + fraction_part)
                    width_fraction = f"{whole_part}-{groups[1]}"
                    width_display = f"{width_fraction}\""
                else:
                    return None

            # 处理新格式 (如 3/8 x 10 Yard)
            elif pattern_type == 'fraction_x_length':
                fraction = Fraction(groups[0])
                width_value = float(fraction)
                width_fraction = groups[0]
                width_display = f"{width_fraction}\""

            # 处理纯分数格式 (如 1/4)
            elif pattern_type in ['fraction_quote', 'fraction_unit']:
                fraction = Fraction(groups[0])
                width_value = float(fraction)
                width_fraction = groups[0]
                width_display = f"{width_fraction}\""

            # 处理小数格式 (如 1.5)
            elif pattern_type == 'decimal_quote':
                width_value = float(groups[0])
                width_fraction = cls._decimal_to_fraction(width_value)
                width_display = f"{width_value}\""

            # 处理整数格式 (如 1)
            elif pattern_type in ['integer_quote', 'number_unit']:
                width_value = float(groups[0])
                width_fraction = str(int(width_value)) if width_value.is_integer() else str(width_value)
                width_display = f"{int(width_value) if width_value.is_integer() else width_value}\""

            else:
                return None

            return {
                'width_value': width_value,
                'width_text': width_text.strip(),
                'width_fraction': width_fraction,
                'width_display': width_display
            }

        except (ValueError, ZeroDivisionError) as e:
            logger.warning(f"⚠️ 数值转换失败: {e}")
            return None
    
    @classmethod
    def _decimal_to_fraction(cls, decimal_value: float) -> str:
        """
        将小数转换为分数表示
        
        Args:
            decimal_value: 小数值
            
        Returns:
            分数字符串
        """
        try:
            fraction = Fraction(decimal_value).limit_denominator(64)  # 限制分母最大为64
            if fraction.denominator == 1:
                return str(fraction.numerator)
            else:
                return str(fraction)
        except:
            return str(decimal_value)
    
    @classmethod
    def normalize_width(cls, width_text: str) -> str:
        """
        标准化宽度文本格式
        
        Args:
            width_text: 原始宽度文本
            
        Returns:
            标准化后的宽度文本
        """
        width_info = cls.parse_width(width_text)
        if width_info:
            return width_info['width_display']
        return width_text
    
    @classmethod
    def compare_widths(cls, width1: str, width2: str) -> bool:
        """
        比较两个宽度是否相等
        
        Args:
            width1: 第一个宽度文本
            width2: 第二个宽度文本
            
        Returns:
            是否相等
        """
        info1 = cls.parse_width(width1)
        info2 = cls.parse_width(width2)
        
        if info1 and info2:
            return abs(info1['width_value'] - info2['width_value']) < 0.001  # 允许小的浮点误差
        
        # 如果无法解析，则进行文本比较
        return cls.normalize_width(width1) == cls.normalize_width(width2)

    @classmethod
    def parse_length(cls, size_text: str) -> Optional[Dict[str, any]]:
        """
        从尺寸文本中解析长度信息

        Args:
            size_text: 尺寸文本，如 "1/4"*50 Yards", "100 feet" 等

        Returns:
            包含长度信息的字典，如果无法解析则返回None
            {
                'length_value': float,  # 长度数值
                'length_text': str,     # 原始长度文本
                'length_unit': str,     # 长度单位
                'length_display': str   # 显示格式
            }
        """
        if not size_text or not isinstance(size_text, str):
            return None

        size_text = size_text.strip()
        logger.debug(f"🔍 解析长度文本: {size_text}")

        # 长度格式正则表达式
        length_patterns = [
            # 新格式: x 数字 Yard (如 x 10 Yard, x 25 Yard)
            (r'x\s*(\d+(?:\.\d+)?)\s*(?:Yards?|yards?|YDS?|yds?|Yard)', 'yards'),

            # 数字+Yards格式: 50 Yards, 100 Yards
            (r'(\d+(?:\.\d+)?)\s*(?:Yards?|yards?|YDS?|yds?)', 'yards'),

            # 数字+Feet格式: 50 Feet, 100 ft
            (r'(\d+(?:\.\d+)?)\s*(?:Feet?|feet?|ft|FT)', 'feet'),

            # 数字+Meters格式: 50 Meters, 100 m
            (r'(\d+(?:\.\d+)?)\s*(?:Meters?|meters?|m|M)', 'meters'),

            # 数字+Inches格式: 50 Inches, 100 in
            (r'(\d+(?:\.\d+)?)\s*(?:Inches?|inches?|in|IN)', 'inches'),
        ]

        for pattern, unit in length_patterns:
            match = re.search(pattern, size_text, re.IGNORECASE)
            if match:
                try:
                    length_value = float(match.group(1))
                    length_text = match.group(0)

                    return {
                        'length_value': length_value,
                        'length_text': length_text.strip(),
                        'length_unit': unit,
                        'length_display': f"{length_value} {unit.title()}"
                    }
                except ValueError as e:
                    logger.warning(f"⚠️ 长度数值转换失败: {e}")
                    continue

        logger.warning(f"❌ 无法解析长度: {size_text}")
        return None

    @classmethod
    def parse_size_components(cls, size_text: str) -> Dict[str, any]:
        """
        解析尺寸文本的所有组件（宽度和长度）

        Args:
            size_text: 尺寸文本，如 "1/4"*50 Yards"

        Returns:
            包含所有尺寸信息的字典
        """
        result = {
            'original_text': size_text,
            'width_info': None,
            'length_info': None,
            'material': None  # 材质信息（如果有的话）
        }

        if not size_text:
            logger.debug(f"尺寸文本为空，返回空结果")
            return result

        # 解析宽度
        width_info = cls.parse_width(size_text)
        if width_info:
            result['width_info'] = width_info
            logger.debug(f"成功解析宽度: {size_text} -> {width_info}")
        else:
            logger.debug(f"无法解析宽度: {size_text}")

        # 解析长度
        length_info = cls.parse_length(size_text)
        if length_info:
            result['length_info'] = length_info
            logger.debug(f"成功解析长度: {size_text} -> {length_info}")
        else:
            logger.debug(f"无法解析长度: {size_text}")

        # 如果宽度和长度都无法解析，记录警告
        if not width_info and not length_info:
            logger.warning(f"⚠️ 尺寸解析失败，宽度和长度都无法识别: {size_text}")

        return result

    @classmethod
    def parse_material(cls, product_description: str, size_text: str = None) -> Optional[str]:
        """
        从产品描述或尺寸文本中解析材质信息

        Args:
            product_description: 产品描述文本
            size_text: 尺寸文本（可选）

        Returns:
            材质名称，如果无法解析则返回None
        """
        if not product_description:
            logger.debug("产品描述为空，无法解析材质")
            return None

        # 常见材质关键词
        material_keywords = [
            'grosgrain',    # 罗纹带
            'satin',        # 缎带
            'velvet',       # 天鹅绒
            'organza',      # 欧根纱
            'cotton',       # 棉质
            'silk',         # 丝绸
            'polyester',    # 聚酯纤维
            'nylon',        # 尼龙
            'lace',         # 蕾丝
            'burlap',       # 麻布
            'jute',         # 黄麻
            'canvas',       # 帆布
        ]

        text_to_search = product_description.lower()
        if size_text:
            text_to_search += " " + size_text.lower()

        logger.debug(f"材质解析 - 搜索文本: {text_to_search}")

        for material in material_keywords:
            if material in text_to_search:
                result = material.title()  # 首字母大写
                logger.debug(f"找到材质: {result} (关键词: {material})")
                return result

        logger.debug(f"未找到匹配的材质关键词，搜索文本: {text_to_search}")
        return None

    @classmethod
    def generate_dimension_key(cls, material: str, length_info: Dict, width_info: Dict = None) -> str:
        """
        生成维度分组键

        Args:
            material: 材质
            length_info: 长度信息
            width_info: 宽度信息（可选）

        Returns:
            维度分组键
        """
        parts = []

        if material:
            parts.append(material)
        else:
            parts.append("UNKNOWN_MATERIAL")

        if length_info:
            length_key = f"{length_info['length_value']}{length_info['length_unit']}"
            parts.append(length_key)
        else:
            parts.append("UNKNOWN_LENGTH")

        if width_info:
            width_key = width_info['width_display'].replace('"', 'inch')
            parts.append(width_key)

        return "-".join(parts)
    
    @classmethod
    def get_supported_width_formats(cls) -> List[str]:
        """
        获取支持的宽度格式示例
        
        Returns:
            支持的格式示例列表
        """
        return [
            '1"',           # 整数英寸
            '1/2"',         # 分数英寸  
            '1-1/2"',       # 混合分数英寸
            '1.5"',         # 小数英寸
            '1 inch',       # 整数+单位
            '1/2 inch',     # 分数+单位
            '1-1/2 inch',   # 混合分数+单位
            '1.5 inches',   # 小数+复数单位
            '3/8"',         # 常见分数
            '5/8"',         # 常见分数
            '1/4"',         # 常见分数
        ]


def test_size_parser():
    """测试尺寸解析器"""
    print("🧪 测试尺寸解析器...")

    # 测试用例
    test_cases = [
        # 现有数据格式
        '1/4"*50 Yards',
        '1/4"*100 Yards',
        '1/2"*50 Yards',
        '1/2"*100 Yards',

        # 用户提到的格式
        '1 inch',
        '1-1/2 inch',
        '1/2 inch',
        '1/4 inch',
        '3/8 inch',
        '5/8 inch',

        # 其他可能格式
        '2"',
        '1.5"',
        '0.75 inches',
        '2-3/4"',
        '7/8 inch',

        # 边界情况
        '',
        None,
        'invalid text',
        '50 Yards',  # 只有长度没有宽度
    ]

    print("\n📏 测试完整尺寸解析:")
    for i, test_case in enumerate(test_cases[:5], 1):  # 只测试前5个完整格式
        print(f"\n{i:2d}. 测试: {test_case}")
        result = SizeParser.parse_size_components(test_case)
        print(f"    原始文本: {result['original_text']}")

        if result['width_info']:
            width = result['width_info']
            print(f"    ✅ 宽度: {width['width_value']} 英寸 ({width['width_display']})")
        else:
            print(f"    ❌ 宽度: 无法解析")

        if result['length_info']:
            length = result['length_info']
            print(f"    ✅ 长度: {length['length_value']} {length['length_unit']} ({length['length_display']})")
        else:
            print(f"    ❌ 长度: 无法解析")

    print("\n📐 测试宽度解析:")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i:2d}. 测试: {test_case}")
        result = SizeParser.parse_width(test_case)
        if result:
            print(f"    ✅ 解析成功:")
            print(f"       数值: {result['width_value']} 英寸")
            print(f"       分数: {result['width_fraction']}")
            print(f"       显示: {result['width_display']}")
        else:
            print(f"    ❌ 解析失败")


if __name__ == "__main__":
    test_size_parser()
