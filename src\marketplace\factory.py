"""
商城工厂类
用于创建和管理不同类型的商城实例
"""

from typing import Dict, List
from .base import BaseMarketplace
from .low_price import LowPriceMarketplace
from .fba import FBAMarketplace


class MarketplaceFactory:
    """商城工厂类"""
    
    # 注册的商城类型
    _marketplace_classes = {
        'low_price': LowPriceMarketplace,
        'fba': FBAMarketplace
    }
    
    @classmethod
    def create_marketplace(cls, marketplace_type: str) -> BaseMarketplace:
        """
        创建商城实例
        
        Args:
            marketplace_type: 商城类型 ('low_price' 或 'fba')
            
        Returns:
            商城实例
            
        Raises:
            ValueError: 不支持的商城类型
        """
        if marketplace_type not in cls._marketplace_classes:
            raise ValueError(f"不支持的商城类型: {marketplace_type}")
        
        marketplace_class = cls._marketplace_classes[marketplace_type]
        return marketplace_class()
    
    @classmethod
    def get_available_marketplaces(cls) -> Dict[str, str]:
        """
        获取可用的商城类型
        
        Returns:
            商城类型字典 {类型代码: 显示名称}
        """
        marketplaces = {}
        for marketplace_type in cls._marketplace_classes:
            marketplace = cls.create_marketplace(marketplace_type)
            marketplaces[marketplace_type] = marketplace.config.display_name
        
        return marketplaces
    
    @classmethod
    def get_marketplace_config(cls, marketplace_type: str) -> Dict[str, any]:
        """
        获取商城配置信息
        
        Args:
            marketplace_type: 商城类型
            
        Returns:
            商城配置字典
        """
        marketplace = cls.create_marketplace(marketplace_type)
        return {
            'name': marketplace.config.name,
            'display_name': marketplace.config.display_name,
            'template_file': marketplace.config.template_file,
            'product_file': marketplace.config.product_file,
            'target_file': marketplace.config.target_file,
            'supported_dimensions': marketplace.config.supported_dimensions,
            'default_dimension': marketplace.config.default_dimension,
            'update_modes': marketplace.config.update_modes,
            'default_update_mode': marketplace.config.default_update_mode
        }
    
    @classmethod
    def register_marketplace(cls, marketplace_type: str, marketplace_class: type):
        """
        注册新的商城类型
        
        Args:
            marketplace_type: 商城类型代码
            marketplace_class: 商城类
        """
        if not issubclass(marketplace_class, BaseMarketplace):
            raise ValueError("商城类必须继承自BaseMarketplace")
        
        cls._marketplace_classes[marketplace_type] = marketplace_class
    
    @classmethod
    def get_supported_dimensions(cls, marketplace_type: str) -> List[str]:
        """
        获取指定商城支持的父体维度
        
        Args:
            marketplace_type: 商城类型
            
        Returns:
            支持的维度列表
        """
        marketplace = cls.create_marketplace(marketplace_type)
        return marketplace.config.supported_dimensions
    
    @classmethod
    def get_supported_update_modes(cls, marketplace_type: str) -> List[str]:
        """
        获取指定商城支持的更新模式
        
        Args:
            marketplace_type: 商城类型
            
        Returns:
            支持的更新模式列表
        """
        marketplace = cls.create_marketplace(marketplace_type)
        return marketplace.config.update_modes
