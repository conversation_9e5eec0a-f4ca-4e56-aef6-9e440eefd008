# 字段映射配置指南 📋

## 概述

本项目使用JSON配置文件来管理各商城的字段映射关系和默认值，配置文件位于：
```
data/config/field_mappings.json
```

## 配置文件结构

### 1. 基本信息
```json
{
  "description": "亚马逊Listing创建工具 - 字段映射配置文件",
  "version": "1.0.0",
  "last_updated": "2025-07-28"
}
```

### 2. 商城配置 (`marketplaces`)

每个商城包含以下配置：

#### 基本信息
- `name`: 商城显示名称
- `description`: 商城描述
- `template_info`: 模板文件信息（仅FBA商城）

#### 字段映射 (`field_mappings`)
支持两种更新模式：
- `partial_update`: 部分更新模式
- `full_update`: 完全更新模式

字段映射格式：
```json
{
  "字段名": {
    "parent": "父体值",           // 父体记录的值
    "child": "子体值",            // 子体记录的值
    "column": 列号,               // 模板中的列号（可选）
    "field_name": "字段显示名",    // 字段显示名称（可选）
    "field_code": "字段代码",     // 字段API代码（可选）
    "description": "字段描述"     // 字段用途描述（可选）
  }
}
```

或者简单格式：
```json
{
  "字段名": {
    "source": "数据源映射",       // 直接映射到数据源
    "description": "字段描述"
  }
}
```

#### 默认值 (`default_values`)
分为父体和子体两种类型：
```json
{
  "parent": {
    "字段名": "默认值"
  },
  "child": {
    "字段名": "默认值"
  }
}
```

### 3. 数据源配置 (`data_sources`)
```json
{
  "target_listing": {
    "file": "已上架低价商城listing.xlsx",
    "sheet": "Sheet1",
    "key_fields": ["MSKU", "color", "size", "品名"]
  }
}
```

### 4. 生成规则配置

#### SKU生成规则 (`sku_generation_rules`)
```json
{
  "low_price": {
    "parent_sku_format": "{SKC}",
    "prefix": "",
    "suffix": ""
  },
  "fba": {
    "parent_sku_format": "FBA-{SKC}",
    "prefix": "FBA-",
    "suffix": ""
  }
}
```

#### 商品名称生成规则 (`item_name_generation_rules`)
```json
{
  "low_price": {
    "format": "{size} {keyword}",
    "prefix": ""
  },
  "fba": {
    "format": "FBA {size} {keyword}",
    "prefix": "FBA"
  }
}
```

## 配置管理工具

使用 `scripts/config_manager.py` 来管理配置：

### 查看配置摘要
```bash
python scripts/config_manager.py summary
```

### 查看特定商城配置
```bash
python scripts/config_manager.py marketplace fba
python scripts/config_manager.py marketplace low_price
```

### 查看字段映射详情
```bash
python scripts/config_manager.py mappings fba partial_update
python scripts/config_manager.py mappings fba full_update
```

### 验证配置文件
```bash
python scripts/config_manager.py validate
```

### 查看数据源配置
```bash
python scripts/config_manager.py sources
```

### 查看生成规则
```bash
python scripts/config_manager.py rules
```

## 修改配置的步骤

### 1. 备份配置文件
```bash
cp data/config/field_mappings.json data/config/field_mappings.json.backup
```

### 2. 编辑配置文件
使用任何文本编辑器打开 `data/config/field_mappings.json`

### 3. 验证配置
```bash
python scripts/config_manager.py validate
```

### 4. 测试配置
```bash
python src/test_marketplace.py
```

## 常见配置场景

### 添加新字段映射
在对应商城的 `field_mappings` 中添加：
```json
{
  "new_field": {
    "source": "target_listing.新字段名",
    "description": "新字段的用途说明"
  }
}
```

### 修改默认值
在对应商城的 `default_values` 中修改：
```json
{
  "parent": {
    "existing_field": "新的默认值"
  }
}
```

### 添加新商城类型
1. 在 `marketplaces` 中添加新商城配置
2. 在 `sku_generation_rules` 中添加SKU生成规则
3. 在 `item_name_generation_rules` 中添加商品名称规则
4. 在代码中创建对应的商城类

## 字段映射值的特殊含义

- `"generated"`: 动态生成的值
- `"generated_parent_sku"`: 引用生成的父体SKU
- `"from_data"`: 从目标数据中获取
- `"target_listing.字段名"`: 映射到目标listing文件的指定字段
- 固定字符串: 直接使用该值

## 注意事项

1. **JSON格式**: 确保配置文件是有效的JSON格式
2. **字段名一致性**: 字段名必须与代码中使用的名称一致
3. **数据源映射**: 确保映射的数据源字段确实存在
4. **备份**: 修改前务必备份原配置文件
5. **测试**: 修改后务必运行测试验证功能正常

## 故障排除

### 配置加载失败
- 检查JSON格式是否正确
- 检查文件路径是否存在
- 查看日志中的错误信息

### 字段映射不生效
- 确认字段名拼写正确
- 检查数据源字段是否存在
- 验证配置文件格式

### 默认值不生效
- 确认字段名与代码中一致
- 检查是否在正确的记录类型（parent/child）中配置

## 配置文件版本管理

建议使用版本控制系统（如Git）来管理配置文件的变更：

```bash
git add data/config/field_mappings.json
git commit -m "更新字段映射配置: 添加新字段XXX"
```

这样可以追踪配置的变更历史，方便回滚和协作。
