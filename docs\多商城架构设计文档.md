# 🏪 多商城架构设计文档

## 📋 项目概述

### 项目名称
亚马逊Listing创建工具 v2.0

### 架构升级目标
将原有的单一低价商城工具升级为支持多种商城类型的通用平台，包括：
- 🏪 低价商城（原有功能）
- 📦 FBA商城（新增功能）
- 🔮 未来可扩展更多商城类型

## 🏗️ 架构设计

### 1. 模块化架构

```
src/
├── marketplace/          # 商城类型管理模块
│   ├── __init__.py
│   ├── base.py          # 商城基类
│   ├── low_price.py     # 低价商城实现
│   ├── fba.py           # FBA商城实现
│   └── factory.py       # 商城工厂
├── config/              # 配置管理模块
│   ├── __init__.py
│   ├── settings.py      # 全局设置
│   └── config_manager.py # 配置管理器
├── core/                # 核心处理逻辑
│   └── template_filler.py # 重构后的模板填充器
├── gui/                 # 图形界面
│   └── main_window.py   # 重构后的主窗口
└── main.py              # 主程序入口
```

### 2. 商城类型管理

#### BaseMarketplace（基类）
定义所有商城类型的通用接口：
- `get_field_mapping()` - 获取字段映射关系
- `get_default_values()` - 获取默认值配置
- `generate_parent_sku()` - 生成父体SKU
- `generate_item_name()` - 生成商品名称
- `validate_data()` - 验证数据

#### LowPriceMarketplace（低价商城）
继承BaseMarketplace，实现低价商城特有逻辑：
- 保持原有的业务规则
- 使用原有的默认值配置
- 兼容现有数据格式

#### FBAMarketplace（FBA商城）
继承BaseMarketplace，实现FBA商城特有逻辑：
- FBA特有的字段映射
- FBA特有的默认值
- 支持更多父体维度（SKC、材质+宽度等）

#### MarketplaceFactory（工厂类）
统一管理商城实例的创建和配置：
- `create_marketplace()` - 创建商城实例
- `get_available_marketplaces()` - 获取可用商城列表
- `get_marketplace_config()` - 获取商城配置

### 3. 配置管理系统

#### Settings（全局设置）
定义应用程序的全局配置：
- 应用信息（名称、版本等）
- 目录路径配置
- UI样式配置
- 默认值配置

#### ConfigManager（配置管理器）
管理用户配置和商城配置：
- 用户偏好设置
- 最近使用文件
- 商城切换记录
- 配置导入导出

## 🎨 界面优化

### 1. 商城类型选择
- 新增商城类型选择下拉框
- 动态显示当前商城信息
- 支持商城间快速切换

### 2. 文件配置区域
- 支持三种文件类型选择（目标文件、产品资料、模板文件）
- 显示当前商城的默认文件信息
- 记录最近使用文件

### 3. 更新模式选择
- 根据商城类型动态显示支持的更新模式
- 部分更新/完全更新切换

### 4. 父体维度配置
- 根据商城类型显示支持的维度
- 动态更新维度说明信息

## 🔧 核心逻辑重构

### 1. TemplateFiller重构
- 支持商城实例注入
- 使用商城的默认值和生成方法
- 动态加载商城配置的默认文件

### 2. 字段映射优化
- 每个商城类型独立的字段映射
- 支持不同的默认值配置
- 灵活的SKU生成规则

### 3. 数据验证增强
- 商城特定的数据验证规则
- 更详细的错误信息
- 数据完整性检查

## 📊 商城对比

| 特性 | 低价商城 | FBA商城 |
|------|----------|---------|
| 模板文件 | 低价商城template.xlsm | FBA商城template.xlsm |
| 产品资料 | 产品资料库-丝带-20250416.xlsx | 产品资料库-FBA.xlsx |
| 支持维度 | SKC | SKC, 材质+宽度 |
| 更新模式 | 部分更新, 完全更新 | 部分更新, 完全更新 |
| 特有字段 | - | 履约中心ID, 库存数量 |
| SKU前缀 | 无 | FBA- |

## 🚀 扩展性设计

### 1. 新商城类型添加
1. 继承BaseMarketplace创建新的商城类
2. 实现必要的抽象方法
3. 在MarketplaceFactory中注册
4. 添加对应的配置文件

### 2. 字段映射扩展
- 支持复杂的字段转换逻辑
- 条件映射和计算字段
- 自定义验证规则

### 3. UI组件扩展
- 商城特定的配置界面
- 高级选项面板
- 批量操作功能

## 🧪 测试策略

### 1. 单元测试
- 商城类方法测试
- 配置管理测试
- 字段映射测试

### 2. 集成测试
- 完整流程测试
- 商城切换测试
- 文件处理测试

### 3. 用户界面测试
- 界面响应测试
- 数据验证测试
- 错误处理测试

## 📈 性能优化

### 1. 配置缓存
- 商城配置缓存
- 用户设置缓存
- 文件路径缓存

### 2. 延迟加载
- 商城实例按需创建
- 大文件分批处理
- UI组件延迟初始化

### 3. 内存管理
- 及时释放不用的数据
- 优化数据结构
- 避免内存泄漏

## 🔒 向后兼容

### 1. 数据兼容
- 保持原有数据格式支持
- 自动迁移旧配置
- 兼容现有模板文件

### 2. 接口兼容
- 保持核心API不变
- 渐进式功能升级
- 平滑的用户体验

### 3. 配置兼容
- 自动检测旧配置
- 智能配置转换
- 默认值回退机制

## 📝 使用说明

### 1. 基本使用流程
1. 启动应用程序
2. 选择商城类型（低价商城/FBA商城）
3. 输入商品关键词
4. 选择文件（可选，使用默认文件）
5. 配置更新模式和父体维度
6. 生成Listing模板

### 2. 高级功能
- 自定义文件选择
- 配置导入导出
- 批量处理模式
- 历史记录查看

## 🔮 未来规划

### v2.1 计划
- 批量处理功能
- 模板自定义编辑器
- 数据分析报告

### v2.2 计划
- 云端配置同步
- 多语言支持
- API接口开放

### v3.0 计划
- 机器学习优化
- 智能字段映射
- 自动化测试集成
