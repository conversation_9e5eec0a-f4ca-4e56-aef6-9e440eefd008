#!/usr/bin/env python3
"""
字段映射配置管理工具
用于查看、验证和管理字段映射配置文件
"""

import sys
import os
import json
from typing import Dict, Any

# 添加项目路径
current_dir = os.path.dirname(__file__)
project_root = os.path.dirname(current_dir)
sys.path.insert(0, os.path.join(project_root, 'src'))

from config.field_mapping_loader import get_field_mapping_loader


def print_separator(title: str = "", char: str = "=", width: int = 60):
    """打印分隔线"""
    if title:
        print(f"\n{char * 5} {title} {char * (width - len(title) - 7)}")
    else:
        print(char * width)


def show_config_summary():
    """显示配置摘要"""
    print_separator("📋 配置文件摘要")
    
    loader = get_field_mapping_loader()
    summary = loader.get_config_summary()
    
    config_path = summary.get('config_file_path') or summary.get('excel_file_path', '未知')
    print(f"📁 配置文件路径: {config_path}")
    print(f"📅 版本: {summary.get('version', '未知')}")
    print(f"🕒 最后更新: {summary.get('last_updated', '未知')}")
    print(f"📊 总字段映射数: {summary.get('total_field_mappings', 0)}")
    print(f"📋 配置来源: {summary.get('source', '未知')}")
    
    print(f"\n🏪 支持的商城类型:")
    for marketplace in summary['marketplaces']:
        print(f"  • {marketplace['name']} ({marketplace['type']})")
        print(f"    描述: {marketplace['description']}")
        print(f"    字段映射数: {marketplace['field_mappings_count']}")


def show_marketplace_config(marketplace_type: str):
    """显示指定商城的配置"""
    print_separator(f"🏪 {marketplace_type.upper()} 商城配置")
    
    loader = get_field_mapping_loader()
    config = loader.get_marketplace_config(marketplace_type)
    
    if not config:
        print(f"❌ 未找到商城类型 '{marketplace_type}' 的配置")
        return
    
    print(f"📛 名称: {config.get('name', 'N/A')}")
    print(f"📝 描述: {config.get('description', 'N/A')}")
    
    # 显示模板信息
    template_info = config.get('template_info', {})
    if template_info:
        print(f"\n📄 模板信息:")
        print(f"  文件: {template_info.get('file', 'N/A')}")
        print(f"  表头行: {template_info.get('header_row', 'N/A')}")
        print(f"  字段代码行: {template_info.get('field_code_row', 'N/A')}")
        print(f"  数据起始行: {template_info.get('data_start_row', 'N/A')}")
        print(f"  总列数: {template_info.get('total_columns', 'N/A')}")
    
    # 显示字段映射
    field_mappings = config.get('field_mappings', {})
    for update_mode, mappings in field_mappings.items():
        print(f"\n🔗 {update_mode} 字段映射 ({len(mappings)} 个字段):")
        for field_name, field_config in mappings.items():
            if isinstance(field_config, dict):
                if 'column' in field_config:
                    print(f"  • {field_name} (列{field_config['column']}): {field_config.get('description', '')}")
                else:
                    print(f"  • {field_name}: {field_config.get('description', str(field_config))}")
            else:
                print(f"  • {field_name}: {field_config}")
    
    # 显示默认值
    default_values = config.get('default_values', {})
    for record_type, defaults in default_values.items():
        print(f"\n⚙️ {record_type} 默认值 ({len(defaults)} 个字段):")
        for field_name, value in defaults.items():
            print(f"  • {field_name}: {value}")


def show_field_mappings(marketplace_type: str, update_mode: str = 'partial_update'):
    """显示字段映射详情"""
    print_separator(f"🔗 {marketplace_type.upper()} - {update_mode} 字段映射")
    
    loader = get_field_mapping_loader()
    mappings = loader.get_field_mappings(marketplace_type, update_mode)
    
    if not mappings:
        print(f"❌ 未找到 {marketplace_type} - {update_mode} 的字段映射")
        return
    
    print(f"📊 共 {len(mappings)} 个字段映射:")
    for field_name, field_config in mappings.items():
        print(f"\n🏷️ {field_name}:")
        if isinstance(field_config, dict):
            for key, value in field_config.items():
                print(f"  {key}: {value}")
        else:
            print(f"  值: {field_config}")


def validate_config():
    """验证配置文件"""
    print_separator("🔍 配置文件验证")
    
    loader = get_field_mapping_loader()
    is_valid, errors = loader.validate_config()
    
    if is_valid:
        print("✅ 配置文件验证通过！")
    else:
        print("❌ 配置文件验证失败:")
        for error in errors:
            print(f"  • {error}")


def show_data_sources():
    """显示数据源配置"""
    print_separator("📊 数据源配置")
    
    loader = get_field_mapping_loader()
    data_sources = loader.get_data_sources_config()
    
    for source_name, source_config in data_sources.items():
        print(f"\n📁 {source_name}:")
        print(f"  文件: {source_config.get('file', 'N/A')}")
        print(f"  工作表: {source_config.get('sheet', 'N/A')}")
        key_fields = source_config.get('key_fields', [])
        print(f"  关键字段 ({len(key_fields)}): {', '.join(key_fields)}")


def show_generation_rules():
    """显示生成规则"""
    print_separator("🔧 生成规则配置")
    
    loader = get_field_mapping_loader()
    config = loader._load_config()
    
    # SKU生成规则
    sku_rules = config.get('sku_generation_rules', {})
    print("\n🏷️ SKU生成规则:")
    for marketplace_type, rules in sku_rules.items():
        print(f"  {marketplace_type}:")
        print(f"    格式: {rules.get('parent_sku_format', 'N/A')}")
        print(f"    前缀: '{rules.get('prefix', '')}'")
        print(f"    后缀: '{rules.get('suffix', '')}'")
    
    # 商品名称生成规则
    item_name_rules = config.get('item_name_generation_rules', {})
    print("\n📝 商品名称生成规则:")
    for marketplace_type, rules in item_name_rules.items():
        print(f"  {marketplace_type}:")
        print(f"    格式: {rules.get('format', 'N/A')}")
        print(f"    前缀: '{rules.get('prefix', '')}'")


def main():
    """主函数"""
    print("🔧 字段映射配置管理工具")
    print("=" * 60)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python scripts/config_manager.py summary              # 显示配置摘要")
        print("  python scripts/config_manager.py validate            # 验证配置文件")
        print("  python scripts/config_manager.py marketplace <type>  # 显示商城配置")
        print("  python scripts/config_manager.py mappings <type> [mode] # 显示字段映射")
        print("  python scripts/config_manager.py sources             # 显示数据源配置")
        print("  python scripts/config_manager.py rules               # 显示生成规则")
        print("\n支持的商城类型: low_price, fba")
        print("支持的映射模式: partial_update, full_update")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == 'summary':
            show_config_summary()
        elif command == 'validate':
            validate_config()
        elif command == 'marketplace':
            if len(sys.argv) < 3:
                print("❌ 请指定商城类型: low_price 或 fba")
                return
            show_marketplace_config(sys.argv[2])
        elif command == 'mappings':
            if len(sys.argv) < 3:
                print("❌ 请指定商城类型: low_price 或 fba")
                return
            marketplace_type = sys.argv[2]
            update_mode = sys.argv[3] if len(sys.argv) > 3 else 'partial_update'
            show_field_mappings(marketplace_type, update_mode)
        elif command == 'sources':
            show_data_sources()
        elif command == 'rules':
            show_generation_rules()
        else:
            print(f"❌ 未知命令: {command}")
    
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
