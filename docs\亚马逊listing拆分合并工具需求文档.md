# 🛠️ 亚马逊listing拆分合并工具 - 项目需求文档

## 📋 项目概述

### 项目名称
亚马逊listing拆分合并工具

### 项目目标
创建一个自动化工具，用于生成亚马逊各商城的listing模板。支持低价商城、FBA商城等多种商城类型，用户提供目标listing信息、产品资料和商品关键词，系统自动生成符合亚马逊要求的上传模板，用户可直接上传到亚马逊后台。

### 项目价值
- 🚀 **提升效率**: 自动化生成listing模板，减少手工操作时间
- 🎯 **确保准确性**: 精准字段映射，避免人为错误
- 💻 **用户友好**: 提供直观的GUI界面，降低使用门槛
- 📊 **标准化流程**: 统一的模板生成规范，确保数据一致性

## 🛠️ 技术实现方案

### 实现方式
通过Python GUI界面实现，用户输入商品关键词，选择目标listing文件，系统基于固定模板和产品资料自动生成亚马逊各商城上传模板。支持低价商城、FBA商城等多种商城类型。

### 技术栈
- **编程语言**: Python 3.8+
- **GUI框架**: tkinter
- **数据处理**: pandas, openpyxl
- **系统环境**: Windows 11

## 📁 数据文件结构

### 1. 目标listing表格
- **文件名**: 已上架低价商城listing.xlsx
- **工作表**: sheet1
- **表头位置**: 第1行
- **关键字段**: MSKU, FNSKU, SKU, ASIN, 状态, Product Description, color, size

### 2. 产品资料表格
- **文件名**: 产品资料库-丝带-20250416.xlsx
- **工作表**: "单个产品"
- **表头位置**: 第1行
- **关键字段**: *SKU（必填）, 图片, 创建时间, 品名（旧）, SPU款名（内部）

### 3. 亚马逊模板文件
- **文件名**: 低价商城template.xlsm
- **工作表**: "Template"
- **表头位置**: 第4行
- **填充起始位置**: 第7行（父体），第8行开始（子体）
- **总字段数**: 288列

## 🔗 核心业务逻辑

### 模板填充逻辑
1. **数据获取**: 读取目标listing表格中的MSKU信息
2. **字段映射**: 根据预定义映射关系进行数据转换
3. **父子体生成**: 基于SKC维度创建父体，基于MSKU创建子体
4. **默认值填充**: 为所有必要字段填充预设默认值

### 父体维度定义
- **SKC维度**: SKC相同的Seller SKU归为一个父体
- **生成规则**: 通过`目标listing表格.SKU`匹配`产品资料.SKC`生成父体Seller SKU

## ⚙️ 功能模块设计

### 1. 父体维度选择（✨ 新增功能）

支持多种父体维度：

#### 1.1 SKC维度（默认）
- **分组规则**: 相同材质 + 相同宽度 + 相同长度 = 一个父体
- **适用场景**: 规格完全相同的产品系列
- **生成逻辑**: 通过`目标listing表格.SKU`匹配`产品资料.SKC`生成父体Seller SKU

#### 1.2 材质+宽度维度
- **分组规则**: 相同材质 + 相同宽度 + 不同长度 = 一个父体
- **适用场景**: 长度规格较多的产品系列
- **生成逻辑**: 基于材质和宽度信息自动分组

#### 1.3 材质+长度维度（✨ 新增）
- **分组规则**: 相同材质 + 相同长度 + 不同宽度 = 一个父体
- **适用场景**: 宽度规格较多的产品系列
- **特殊功能**: 支持用户选择包含哪些宽度规格
- **支持格式**:
  - 分数格式: 1/4", 3/8", 5/8" 等
  - 混合分数: 1-1/2", 2-3/4" 等
  - 整数格式: 1", 2", 3" 等
  - 小数格式: 1.5", 2.25" 等
  - 带单位格式: 1 inch, 1/2 inch 等

#### 1.4 商城维度支持情况

| 商城类型 | SKC维度 | 材质+宽度维度 | 材质+长度维度 |
|---------|---------|---------------|---------------|
| 🏪 低价商城 | ✅ 支持 | ❌ 暂不支持 | ✅ 支持 |
| 📦 FBA商城 | ✅ 支持 | ✅ 支持 | ✅ 支持 |

**说明**:
- **低价商城**: 支持SKC维度和材质+长度维度，适合基础的产品分组需求
- **FBA商城**: 支持所有维度类型，提供最完整的父体分组功能

### 2. 更新模式支持

#### 部分更新模式（推荐）
**父体必要字段**:
- Seller SKU, Record Action, Product Type, Item Name, Brand Name
- Item Type Keyword, Product Description, Bullet Point
- Parentage Level, Child Relationship Type, Variation Theme Name
- Country of Origin, Are batteries required?, Dangerous Goods Regulations

**子体必要字段**:
- Seller SKU, Record Action, Product Type, Brand Name, Color
- Parentage Level, Child Relationship Type, Parent SKU
- Variation Theme Name, Country of Origin, Are batteries required?
- Dangerous Goods Regulations

#### 完全更新模式
**父体必要字段**: 在部分更新基础上增加Color字段
**子体必要字段**: 在部分更新基础上增加Item Type Keyword, Product Description, Bullet Point字段

### 3. 商品关键词输入
- **输入方式**: GUI界面手动输入
- **使用场景**: 用于生成Item Name和Item Type Keyword

## 📊 字段映射关系

### 核心映射规则
| 源字段 | 目标字段 | 映射类型 |
|--------|----------|----------|
| 目标listing表格.MSKU | 模板.Seller SKU（子体） | 直接映射 |
| 目标listing表格.Product Description | 模板.Product Description | 直接映射 |
| 目标listing表格.color | 模板.Color | 直接映射 |
| 目标listing表格.size + 商品关键词 | 模板.Item Name | 组合生成 |

### 默认值配置

#### 部分更新 - 父体字段
| 字段名 | 默认值 | 生成逻辑 |
|--------|--------|----------|
| Seller SKU | 动态生成 | 基于SKC维度匹配 |
| Record Action | "Full Update" | 固定值 |
| Product Type | "HOME" | 固定值 |
| Item Name | 动态生成 | `目标listing表格.size + 商品关键词` |
| Brand Name | "GENERIC" | 固定值 |
| Item Type Keyword | 用户输入 | 商品关键词 |
| Product Description | 动态生成 | 等于Item Name |
| Bullet Point | 动态生成 | 等于Item Name |
| Parentage Level | "Parent" | 固定值 |
| Child Relationship Type | "Variation" | 固定值 |
| Variation Theme Name | "COLOR" | 固定值 |
| Country of Origin | "China" | 固定值 |
| Are batteries required? | "No" | 固定值 |
| Dangerous Goods Regulations | "Not Applicable" | 固定值 |

#### 部分更新 - 子体字段
| 字段名 | 默认值 | 生成逻辑 |
|--------|--------|----------|
| Seller SKU | 目标listing表格.MSKU | 直接映射 |
| Record Action | "Partial Update" | 固定值 |
| Product Type | "HOME" | 固定值 |
| Brand Name | "GENERIC" | 固定值 |
| Color | 目标listing表格.color | 直接映射 |
| Parentage Level | "Child" | 固定值 |
| Child Relationship Type | "Variation" | 固定值 |
| Parent SKU | 生成的模版.Template.A7 | 引用父体Seller SKU |
| Variation Theme Name | "COLOR" | 固定值 |
| Country of Origin | "China" | 固定值 |
| Are batteries required? | "No" | 固定值 |
| Dangerous Goods Regulations | "Not Applicable" | 固定值 |

## 📝 填充位置规范

### 父体填充规则
- **起始位置**: 模板第7行
- **填充逻辑**: 每个SKC维度生成一条父体记录
- **排列方式**: 如果存在多个父体，在对应父体子体填充完成后，继续填充下一个父体

### 子体填充规则
- **起始位置**: 模板第8行开始
- **填充逻辑**: 每个MSKU生成一条子体记录
- **关联关系**: 子体的Parent SKU字段引用对应父体的Seller SKU

## 🎯 示例说明

### Item Name生成示例
- **输入**: size = "3/8"-100 Yards", 商品关键词 = "satin ribbon"
- **输出**: Item Name = "3/8"-100 Yards satin ribbon"

## ⚠️ 重要约束条件

### 数据处理原则
1. **精准匹配**: 所有字段之间采用精准匹配，不进行任何智能匹配或模糊匹配
2. **数据完整性**: 确保所有必要字段都有对应的数据源或默认值
3. **格式一致性**: 严格按照亚马逊模板格式要求进行数据填充

### 系统限制
1. **文件格式**: 仅支持Excel格式文件（.xlsx, .xlsm）
2. **编码要求**: 建议使用UTF-8编码
3. **数据量限制**: 单次处理建议不超过1000条记录

## 🔄 版本管理

### 当前版本: v1.0.0
- ✅ 实现核心模板填充功能
- ✅ 支持GUI界面操作
- ✅ 支持部分更新和完全更新模式
- ✅ 完整的字段映射关系
- ✅ 数据验证和错误处理

### 未来规划
- 🔮 支持更多父体维度选择
- 🔮 批量处理优化
- 🔮 模板自定义配置
- 🔮 数据导入导出功能增强

## 🔄 业务流程设计

### 用户操作流程
1. **启动程序**: 运行 `python src/main.py` 启动GUI界面
2. **输入关键词**: 在界面中输入商品关键词（如："satin ribbon"）
3. **选择文件**（可选）:
   - 目标listing表格 (默认: 已上架低价商城listing.xlsx)
   - 产品资料表格 (默认: 产品资料库-丝带-20250416.xlsx)
   - 亚马逊模板 (默认: 低价商城template.xlsm)
4. **选择更新模式**: 部分更新（推荐）或完全更新
5. **生成模板**: 点击"生成Listing模板"按钮
6. **查看结果**: 在`data/output/`目录中查看生成的Excel文件
7. **上传亚马逊**: 用户手动将生成的模板上传到亚马逊后台

### 系统处理流程
1. **数据验证**: 检查输入文件格式和必要字段
2. **数据读取**: 从三个Excel文件中读取相关数据
3. **SKC分组**: 根据SKC维度对产品进行分组
4. **父体生成**: 为每个SKC组生成一条父体记录
5. **子体生成**: 为每个MSKU生成一条子体记录
6. **字段映射**: 根据映射关系填充所有字段
7. **模板输出**: 将处理结果写入新的Excel模板文件

## 🧪 测试规范

### 单元测试要求
- **数据读取测试**: 验证Excel文件读取功能
- **字段映射测试**: 验证映射关系的正确性
- **默认值测试**: 验证默认值填充逻辑
- **父子体关联测试**: 验证Parent SKU引用关系

### 集成测试要求
- **完整流程测试**: 端到端的模板生成测试
- **异常处理测试**: 文件缺失、格式错误等异常情况
- **性能测试**: 大数据量处理性能验证
- **界面交互测试**: GUI界面功能验证

### 测试数据要求
- **标准测试数据**: 包含完整字段的正常数据
- **边界测试数据**: 空值、特殊字符等边界情况
- **异常测试数据**: 格式错误、字段缺失等异常数据

## 🔧 配置管理

### 默认配置
```python
DEFAULT_CONFIG = {
    "input_files": {
        "target_listing": "data/input/已上架低价商城listing.xlsx",
        "product_data": "data/input/产品资料库-丝带-20250416.xlsx",
        "amazon_template": "data/input/低价商城template.xlsm"
    },
    "output_path": "data/output/",
    "log_path": "data/logs/",
    "update_mode": "partial",  # partial | full
    "parent_dimension": "skc"  # skc | material_width | custom
}
```

### 可配置项
- **文件路径**: 支持自定义输入输出路径
- **更新模式**: 部分更新/完全更新切换
- **父体维度**: 未来支持多种分组维度
- **日志级别**: DEBUG/INFO/WARNING/ERROR

## 📊 数据质量控制

### 输入数据验证
1. **文件存在性检查**: 确保所有输入文件存在
2. **格式验证**: 验证Excel文件格式和工作表结构
3. **必要字段检查**: 确保关键字段存在且非空
4. **数据类型验证**: 验证字段数据类型符合要求

### 输出数据验证
1. **字段完整性**: 确保所有必要字段都已填充
2. **数据一致性**: 验证父子体关联关系正确
3. **格式规范性**: 确保输出格式符合亚马逊要求
4. **数据准确性**: 验证映射和计算结果正确

## 🚨 错误处理机制

### 异常分类
1. **文件异常**: 文件不存在、权限不足、格式错误
2. **数据异常**: 必要字段缺失、数据类型错误、值超出范围
3. **业务异常**: SKC匹配失败、父子体关联错误
4. **系统异常**: 内存不足、磁盘空间不足

### 处理策略
1. **友好提示**: 向用户显示清晰的错误信息
2. **日志记录**: 详细记录错误信息到日志文件
3. **优雅降级**: 部分数据错误时继续处理其他数据
4. **回滚机制**: 处理失败时清理临时文件

## 📈 性能优化

### 内存优化
- **分批处理**: 大数据量时分批读取和处理
- **数据结构优化**: 使用高效的数据结构存储中间结果
- **内存释放**: 及时释放不再使用的数据对象

### 处理速度优化
- **并行处理**: 利用多线程处理独立的数据块
- **缓存机制**: 缓存重复计算的结果
- **算法优化**: 使用高效的查找和匹配算法

## 🔐 安全考虑

### 数据安全
- **文件权限**: 确保输出文件的适当权限设置
- **数据脱敏**: 在日志中避免记录敏感信息
- **临时文件清理**: 及时清理处理过程中的临时文件

### 系统安全
- **输入验证**: 严格验证用户输入，防止注入攻击
- **路径安全**: 防止路径遍历攻击
- **资源限制**: 限制内存和CPU使用，防止资源耗尽

## 📞 技术支持

### 文档资源
1. **项目文档**: `docs/`目录
2. **字段映射关系**: `docs/字段映射关系.md`
3. **项目结构说明**: `docs/项目结构说明.md`
4. **程序日志**: `data/logs/`目录

### 故障排除
1. **常见问题**: 查看README.md中的故障排除章节
2. **日志分析**: 查看详细的程序执行日志
3. **测试验证**: 运行测试脚本验证功能正常性
4. **版本检查**: 确认Python和依赖包版本符合要求

### 联系方式
- **技术支持**: 亚马逊listing工具技术团队
- **问题反馈**: 通过项目issue系统提交
- **功能建议**: 欢迎提出改进建议和新功能需求

## 🆕 新增功能详解

### 材质+长度维度功能

#### 功能概述
当选择"材质+长度维度"时，系统会：
1. 自动解析产品的材质信息（如Grosgrain、Satin、Velvet等）
2. 自动解析产品的长度信息（如50 Yards、100 Yards等）
3. 识别所有可用的宽度规格
4. 弹出宽度选择对话框，让用户确认要包含的宽度

#### 宽度识别能力
系统支持识别以下宽度格式：
- **分数格式**: 1/4", 3/8", 5/8", 7/8" 等
- **混合分数**: 1-1/2", 2-3/4", 3-1/8" 等
- **整数格式**: 1", 2", 3" 等
- **小数格式**: 1.5", 2.25", 0.75" 等
- **带单位格式**: 1 inch, 1/2 inch, 1-1/2 inches 等

#### 用户交互流程
1. 用户选择"材质+长度维度"
2. 系统分析数据，识别所有材质-长度组合
3. 对于每个组合，弹出宽度选择对话框
4. 用户可以：
   - 查看所有可用宽度（按数值排序）
   - 选择/取消选择特定宽度
   - 使用"全选"/"全不选"快捷操作
   - 查看已选择的宽度数量
5. 确认后，系统生成相应的父子体结构

#### 技术实现
- **尺寸解析器**: `SizeParser`类，支持复杂的宽度格式解析
- **材质识别**: 基于关键词匹配，支持常见丝带材质
- **宽度选择界面**: 图形化对话框，支持批量操作
- **维度分组**: 智能分组算法，确保相同材质+长度的产品归为一个父体

## 📝 更新日志

### v2.1.1 (2025-07-29)
- ✨ 低价商城新增材质+长度维度支持
- 🔧 统一维度命名规范（MATERIAL_LENGTH）
- 📚 更新商城维度支持文档

### v2.1.0 (2025-07-29)
- ✨ FBA商城新增材质+长度维度支持
- ✨ 新增宽度格式智能识别
- ✨ 新增用户宽度选择界面
- 🔧 优化FBA模板字段映射
- 📚 更新需求文档和功能说明

### v2.0.0 (2025-07-28)
- ✨ 新增多商城支持架构
- ✨ 新增FBA商城支持
- 🔧 重构字段映射系统
- 📚 完善文档和配置指南

### v1.0.0 (2025-07-27)
- 🎉 初始版本发布
- ✅ 支持低价商城模板生成
- ✅ 基础GUI界面
- ✅ SKC维度父体生成
