"""
变体属性解析器 - 用于解析变体属性格式的文本
支持 [size:xxx];[color:xxx] 格式的变体属性解析
"""

import re
from typing import Dict, Optional, List, Tuple, Any
from loguru import logger


class VariantAttributeParser:
    """变体属性解析器，专门处理 [attribute:value] 格式的变体属性"""
    
    # 变体属性格式正则表达式
    VARIANT_PATTERN = r'\[([^:]+):([^\]]+)\]'
    
    @classmethod
    def parse_variant_attributes(cls, variant_text: str) -> Optional[Dict[str, str]]:
        """
        解析变体属性文本，提取所有属性键值对
        
        Args:
            variant_text: 变体属性文本，如 "[size:1-1/2" X 50 Yards];[color:Ivory]"
            
        Returns:
            包含所有属性的字典，如果无法解析则返回None
            {
                'size': '1-1/2" X 50 Yards',
                'color': 'Ivory'
            }
        """
        if not variant_text or not isinstance(variant_text, str):
            return None
            
        variant_text = variant_text.strip()
        logger.debug(f"🔍 解析变体属性文本: {variant_text}")
        
        # 查找所有匹配的属性
        matches = re.findall(cls.VARIANT_PATTERN, variant_text)
        
        if not matches:
            logger.warning(f"❌ 无法解析变体属性: {variant_text}")
            return None
        
        # 构建属性字典
        attributes = {}
        for attr_name, attr_value in matches:
            attr_name = attr_name.strip().lower()
            attr_value = attr_value.strip()
            attributes[attr_name] = attr_value
            logger.debug(f"✅ 解析到属性: {attr_name} = {attr_value}")
        
        return attributes
    
    @classmethod
    def extract_size_and_color(cls, variant_text: str) -> Tuple[Optional[str], Optional[str]]:
        """
        从变体属性文本中提取尺寸和颜色信息
        
        Args:
            variant_text: 变体属性文本
            
        Returns:
            (size, color) 元组，如果某个属性不存在则为None
        """
        attributes = cls.parse_variant_attributes(variant_text)
        
        if not attributes:
            return None, None
        
        size = attributes.get('size')
        color = attributes.get('color')
        
        return size, color
    
    @classmethod
    def parse_size_from_variant(cls, variant_text: str) -> Optional[Dict[str, Any]]:
        """
        从变体属性中解析尺寸信息，并进一步解析尺寸的宽度和长度
        
        Args:
            variant_text: 变体属性文本
            
        Returns:
            包含详细尺寸信息的字典
        """
        size, _ = cls.extract_size_and_color(variant_text)
        
        if not size:
            return None
        
        # 导入尺寸解析器来进一步解析尺寸
        try:
            from .width_parser import SizeParser
            return SizeParser.parse_size_components(size)
        except ImportError:
            logger.warning("无法导入SizeParser，返回基本尺寸信息")
            return {
                'original_text': size,
                'width_info': None,
                'length_info': None,
                'material': None
            }
    
    @classmethod
    def convert_to_standard_format(cls, variant_text: str) -> Dict[str, str]:
        """
        将变体属性转换为标准格式
        
        Args:
            variant_text: 变体属性文本
            
        Returns:
            标准格式的属性字典
        """
        attributes = cls.parse_variant_attributes(variant_text)
        
        if not attributes:
            return {}
        
        standard_format = {}
        
        # 处理尺寸
        if 'size' in attributes:
            size_value = attributes['size']
            standard_format['size'] = size_value
            
            # 尝试解析尺寸的详细信息
            size_info = cls.parse_size_from_variant(variant_text)
            if size_info:
                width_info = size_info.get('width_info')
                length_info = size_info.get('length_info')
                
                if width_info:
                    standard_format['width'] = width_info.get('width_display', size_value)
                    standard_format['width_value'] = width_info.get('width_value')
                    standard_format['width_fraction'] = width_info.get('width_fraction')
                
                if length_info:
                    standard_format['length'] = length_info.get('length_display', '')
                    standard_format['length_value'] = length_info.get('length_value')
                    standard_format['length_unit'] = length_info.get('length_unit')
        
        # 处理颜色
        if 'color' in attributes:
            color_value = attributes['color']
            standard_format['color'] = color_value
            
            # 颜色标准化处理
            standard_format['color_normalized'] = cls._normalize_color_name(color_value)
        
        return standard_format
    
    @classmethod
    def _normalize_color_name(cls, color_name: str) -> str:
        """
        标准化颜色名称
        
        Args:
            color_name: 原始颜色名称
            
        Returns:
            标准化后的颜色名称
        """
        if not color_name:
            return color_name
        
        # 颜色名称映射表
        color_mapping = {
            'ivory': 'Ivory',
            'purple bright': 'Purple Bright',
            'coral': 'Coral',
            'gray': 'Gray',
            'grey': 'Gray',
            'purple lilac mist': 'Purple Lilac Mist',
            'green hunter': 'Green Hunter',
            'gold old': 'Gold Old',
            'gold antique': 'Gold Antique',
            'yellow lemon': 'Yellow Lemon',
            'blue turquoise': 'Blue Turquoise',
            'blue royal': 'Blue Royal',
            'peach': 'Peach',
            'purple lilac': 'Purple Lilac',
            'gold rose': 'Gold Rose',
            'beige': 'Beige',
            'burgundy': 'Burgundy',
            'purple': 'Purple',
            'silver': 'Silver',
            'gold light': 'Gold Light',
            'yellow daffodil': 'Yellow Daffodil',
            'copper': 'Copper'
        }
        
        color_lower = color_name.lower().strip()
        return color_mapping.get(color_lower, color_name.title())
    
    @classmethod
    def batch_parse_variants(cls, variant_list: List[str]) -> List[Dict[str, Any]]:
        """
        批量解析变体属性列表
        
        Args:
            variant_list: 变体属性文本列表
            
        Returns:
            解析结果列表
        """
        results = []
        
        for i, variant_text in enumerate(variant_list):
            try:
                parsed_data = cls.convert_to_standard_format(variant_text)
                parsed_data['original_text'] = variant_text
                parsed_data['index'] = i
                results.append(parsed_data)
                logger.debug(f"✅ 成功解析变体 {i+1}: {parsed_data}")
            except Exception as e:
                logger.warning(f"⚠️ 解析变体 {i+1} 时出错: {e}")
                results.append({
                    'original_text': variant_text,
                    'index': i,
                    'error': str(e)
                })
        
        return results
    
    @classmethod
    def validate_variant_format(cls, variant_text: str) -> bool:
        """
        验证变体属性格式是否正确
        
        Args:
            variant_text: 变体属性文本
            
        Returns:
            格式是否正确
        """
        if not variant_text or not isinstance(variant_text, str):
            return False
        
        # 检查是否包含至少一个有效的属性格式
        matches = re.findall(cls.VARIANT_PATTERN, variant_text)
        return len(matches) > 0
    
    @classmethod
    def get_supported_attributes(cls) -> List[str]:
        """
        获取支持的属性列表
        
        Returns:
            支持的属性名称列表
        """
        return ['size', 'color', 'material', 'style', 'pattern']


# 测试函数
def test_variant_parser():
    """测试变体属性解析器"""
    test_cases = [
        "[size:1-1/2\" X 50 Yards];[color:Ivory]",
        "[size:3/8\" X 50 Yards];[color:Purple Bright]",
        "[size:3/8\" x 25 Yards];[color:Coral]",
        "[size:1/4\" x 25 Yards];[color:Gray]",
        "[size:1/4\" x 25 Yards];[color:Purple Lilac Mist]",
        "[size:3/8\" x 25 Yards];[color:Green Hunter]",
        "[size:1\" X 50 Yards];[color:Gold Old]"
    ]
    
    parser = VariantAttributeParser()
    
    print("🧪 测试变体属性解析器")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试用例: {test_case}")
        
        # 基本解析
        attributes = parser.parse_variant_attributes(test_case)
        print(f"   基本解析: {attributes}")
        
        # 提取尺寸和颜色
        size, color = parser.extract_size_and_color(test_case)
        print(f"   尺寸: {size}")
        print(f"   颜色: {color}")
        
        # 标准格式转换
        standard = parser.convert_to_standard_format(test_case)
        print(f"   标准格式: {standard}")


if __name__ == "__main__":
    test_variant_parser()
