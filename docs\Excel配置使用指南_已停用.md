# 超简单Excel配置指南 📊

## 🎯 为什么这么简单？

现在的Excel配置表格就像填空题一样简单：
- ✅ **一看就懂** - 每个字段都有中文说明
- ✅ **明确来源** - 清楚标明数据从哪个表格的哪一列来
- ✅ **填空式操作** - 只需要修改"如何填充"列
- ✅ **有具体示例** - 每个字段都有填充示例
- ✅ **不会出错** - 不用担心语法问题

## 📁 配置文件在哪里？

```
📂 项目文件夹
└── 📂 data
    └── 📂 config
        └── 📄 字段映射配置.xlsx  ← 就是这个文件！
```

## 📋 Excel文件里有什么？

### 📊 工作表说明
- **FBA商城字段映射** - 亚马逊FBA的字段设置
- **低价商城字段映射** - 低价商城的字段设置
- **数据来源说明** - 告诉你数据文件都在哪里
- **配置说明** - 详细的使用方法

### 📝 表格列说明（新版本更直白）
| 列名 | 说明 | 能不能改 |
|------|------|----------|
| 模板字段名 | 上传模板中的字段名称 | ❌ 不要改 |
| 模板列号 | 在Excel模板中的列位置 | ❌ 不要改 |
| **父体如何填充** | 父体记录怎么填这个字段 | ✅ **可以改** |
| **子体如何填充** | 子体记录怎么填这个字段 | ✅ **可以改** |
| 数据来源表格 | 数据从哪个Excel文件来 | ℹ️ 参考信息 |
| 数据来源列名 | 数据从哪一列来 | ℹ️ 参考信息 |
| 填充示例 | 实际填充的内容示例 | ℹ️ 参考信息 |
| 字段说明 | 这个字段是干什么用的 | ℹ️ 参考信息 |

## 🔧 超简单修改方法

### 📖 就像填空题一样简单！

1. **打开Excel文件**
   ```
   双击打开: data/config/字段映射配置.xlsx
   ```

2. **选择商城类型**
   - 📦 **FBA商城字段映射** - 如果要改亚马逊FBA的设置
   - 🏪 **低价商城字段映射** - 如果要改低价商城的设置

3. **找到要修改的字段行**
   - 看"模板字段名"列，找到你要改的字段
   - 比如要改品牌，就找"Brand"那一行

4. **修改填充方式**
   - 只改这两列：**"父体如何填充"** 和 **"子体如何填充"**
   - 其他列都不要动！

5. **保存文件**
   ```
   按 Ctrl + S 保存
   ```

6. **重启程序**
   - 关闭listing创建工具
   - 重新打开，新设置就生效了！

### 方法二：使用简单配置编辑器

1. **启动配置编辑器**
   ```bash
   python scripts/simple_config_editor.py
   ```

2. **在GUI界面中编辑**
   - 选择商城类型
   - 双击要编辑的字段
   - 在弹出对话框中修改值
   - 点击保存

3. **保存配置**
   - 点击"💾 保存配置"按钮

## 📝 填充方式说明（超简单版）

### 🤖 程序自动生成
```
程序自动生成
```
**什么意思？** 程序会自动算出这个值，你不用管

**例子：** SKU会自动生成为 `FBA-DL-LW-PZ-PT-02-050`

### 📝 固定填写→具体值
```
固定填写→GENERIC
固定填写→China
固定填写→Update
```
**什么意思？** 每次都填写相同的固定内容

**例子：** 品牌永远填写"GENERIC"

### 📊 来自→文件名→列名
```
来自→已上架低价商城listing.xlsx→MSKU列
来自→已上架低价商城listing.xlsx→color列
来自→已上架低价商城listing.xlsx→品名列
```
**什么意思？** 从指定的Excel文件的指定列获取数据

**例子：** 颜色信息从"已上架低价商城listing.xlsx"文件的"color"列获取

### 🔗 引用父体的SKU
```
引用父体的SKU
```
**什么意思？** 子体记录引用对应父体的SKU

**例子：** 子体的Parent SKU字段会自动填入父体的SKU

### ❌ 不填充
```
不填充
留空
```
**什么意思？** 这个字段不填任何内容，保持空白

## 🎨 实际修改例子（一看就会）

### 例子1：我想把品牌改成自己的品牌
**现在的设置：**
- 模板字段名：Brand
- 父体如何填充：`固定填写→GENERIC`
- 子体如何填充：`固定填写→GENERIC`

**要改成：**
- 父体如何填充：`固定填写→MyBrand`  ← 只改这里
- 子体如何填充：`固定填写→MyBrand`  ← 只改这里

### 例子2：我想把原产国改成美国
**现在的设置：**
- 模板字段名：Country/Region of Origin
- 父体如何填充：`固定填写→China`
- 子体如何填充：`固定填写→China`

**要改成：**
- 父体如何填充：`固定填写→USA`  ← 只改这里
- 子体如何填充：`固定填写→USA`  ← 只改这里

### 例子3：我想从其他列获取价格数据
**现在的设置：**
- 模板字段名：Your Price
- 子体如何填充：`来自→已上架低价商城listing.xlsx→price列`

**要改成：**
- 子体如何填充：`来自→已上架低价商城listing.xlsx→新价格列`  ← 只改这里

### 例子4：我不想填充某个字段
**要改成：**
- 父体如何填充：`不填充`  ← 改成这个
- 子体如何填充：`不填充`  ← 改成这个

## 🛠️ 配置管理工具

### 查看配置摘要
```bash
python scripts/config_manager.py summary
```

### 查看FBA商城配置
```bash
python scripts/config_manager.py marketplace fba
```

### 验证配置文件
```bash
python scripts/config_manager.py validate
```

### 启动GUI编辑器
```bash
python scripts/simple_config_editor.py
```

## ⚠️ 注意事项

### 必须遵守的规则
1. **不要修改"字段名"列** - 这会导致程序无法识别字段
2. **不要删除行** - 删除字段行会导致功能缺失
3. **不要修改工作表名称** - 程序依赖固定的工作表名称
4. **保持Excel格式** - 不要转换为其他格式

### 推荐做法
1. **修改前备份** - 复制一份Excel文件作为备份
2. **小步修改** - 每次只修改少量字段，便于排查问题
3. **及时测试** - 修改后立即测试功能是否正常
4. **记录变更** - 在描述列或单独文档中记录修改原因

## 🔍 故障排除

### 配置不生效
1. 检查是否保存了Excel文件
2. 重启程序或重新加载配置
3. 检查字段名是否被意外修改

### Excel文件打不开
1. 检查文件是否存在
2. 检查文件是否被其他程序占用
3. 尝试重新创建配置文件：
   ```bash
   python scripts/create_excel_config.py
   ```

### 程序报错
1. 运行配置验证：
   ```bash
   python scripts/config_manager.py validate
   ```
2. 查看错误信息，检查对应字段配置
3. 恢复备份文件

## 💡 高级技巧

### 批量修改
1. 在Excel中使用查找替换功能（Ctrl+H）
2. 可以快速将所有"China"替换为"USA"

### 条件格式
1. 可以给重要字段添加颜色标记
2. 便于快速识别需要关注的配置

### 数据验证
1. 可以为某些列设置下拉选项
2. 避免输入错误的值

## 🎉 总结

Excel配置方式让字段映射管理变得简单直观：
- 📊 **表格化管理** - 所有配置一目了然
- 🖱️ **点击即编辑** - 无需学习复杂语法
- 💾 **保存即生效** - 修改后重启程序即可
- 🔄 **易于备份** - 直接复制Excel文件

现在您可以像编辑普通Excel表格一样轻松管理字段映射配置了！
