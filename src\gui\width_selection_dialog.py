"""
宽度选择对话框
用于材质+长度维度时让用户选择包含哪些宽度
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Set, Optional
from loguru import logger

# 导入尺寸解析工具
try:
    from ..utils.width_parser import SizeParser
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from utils.width_parser import SizeParser


class WidthSelectionDialog:
    """宽度选择对话框类"""
    
    def __init__(self, parent, available_widths: List[str], material: str, length: str):
        """
        初始化宽度选择对话框
        
        Args:
            parent: 父窗口
            available_widths: 可用的宽度列表
            material: 材质名称
            length: 长度信息
        """
        self.parent = parent
        self.available_widths = available_widths
        self.material = material
        self.length = length
        self.selected_widths: Set[str] = set()
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🎨 选择宽度规格")
        self.dialog.geometry("500x500")  # 增加高度确保按钮可见
        self.dialog.resizable(True, True)
        
        # 设置模态
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 绑定事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
        
    def _center_window(self):
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        
        # 获取屏幕尺寸
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        
        # 获取窗口尺寸
        window_width = self.dialog.winfo_reqwidth()
        window_height = self.dialog.winfo_reqheight()
        
        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主容器
        container = ttk.Frame(self.dialog)
        container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 内容区域
        content_frame = ttk.Frame(container)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 按钮区域 - 固定高度
        button_frame = ttk.Frame(container)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 标题
        title_label = ttk.Label(
            content_frame,
            text="🎨 材质+长度维度 - 宽度选择",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 10))

        # 信息框架
        info_frame = ttk.LabelFrame(content_frame, text="📋 维度信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 材质信息
        material_label = ttk.Label(info_frame, text=f"🧵 材质: {self.material}")
        material_label.pack(anchor=tk.W)
        
        # 长度信息
        length_label = ttk.Label(info_frame, text=f"📏 长度: {self.length}")
        length_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 说明文本
        desc_label = ttk.Label(
            info_frame, 
            text="请选择要包含在同一个父体中的宽度规格。相同材质和长度、不同宽度的产品将被归为一个父体。",
            wraplength=500,
            foreground="gray"
        )
        desc_label.pack(anchor=tk.W, pady=(10, 0))
        
        # 宽度选择框架 - 限制高度，不要expand=True
        selection_frame = ttk.LabelFrame(content_frame, text="📐 可用宽度规格", padding="10")
        selection_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 创建滚动框架 - 设置合适的固定高度确保按钮可见
        canvas = tk.Canvas(selection_frame, height=100)
        scrollbar = ttk.Scrollbar(selection_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 宽度复选框
        self.width_vars = {}
        self.width_checkboxes = {}
        
        # 解析和排序宽度
        width_data = []
        for width in self.available_widths:
            width_info = SizeParser.parse_width(width)
            if width_info:
                width_data.append((width_info['width_value'], width, width_info['width_display']))
            else:
                width_data.append((999, width, width))  # 无法解析的放在最后
        
        # 按数值排序
        width_data.sort(key=lambda x: x[0])
        
        for _, original_width, display_width in width_data:
            var = tk.BooleanVar()
            self.width_vars[original_width] = var
            
            checkbox = ttk.Checkbutton(
                scrollable_frame,
                text=f"{display_width} ({original_width})",
                variable=var,
                command=self._on_width_changed
            )
            checkbox.pack(anchor=tk.W, pady=2)
            self.width_checkboxes[original_width] = checkbox
        
        # 配置滚动
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 全选/全不选按钮 - 放在滚动区域下面
        select_button_frame = ttk.Frame(content_frame)
        select_button_frame.pack(fill=tk.X, pady=(10, 5))

        select_all_btn = ttk.Button(
            select_button_frame,
            text="✅ 全选",
            command=self._select_all,
            width=10
        )
        select_all_btn.pack(side=tk.LEFT, padx=(0, 5))

        select_none_btn = ttk.Button(
            select_button_frame,
            text="❌ 全不选",
            command=self._select_none,
            width=10
        )
        select_none_btn.pack(side=tk.LEFT)

        # 统计信息 - 放在全选按钮之后
        self.stats_label = ttk.Label(content_frame, text="已选择: 0 个宽度", foreground="blue")
        self.stats_label.pack(pady=(5, 0))

        # 创建底部按钮
        logger.debug("🔘 创建宽度选择对话框按钮")
        self._create_buttons(button_frame)

        # 默认选择所有宽度
        self._select_all()

    def _create_buttons(self, button_frame):
        """创建底部按钮"""
        # 确定按钮
        confirm_btn = ttk.Button(
            button_frame,
            text="✅ 确定",
            command=self._on_confirm,
            style="Accent.TButton"
        )
        confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 取消按钮
        cancel_btn = ttk.Button(
            button_frame,
            text="❌ 取消",
            command=self._on_cancel
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 返回按钮
        back_btn = ttk.Button(
            button_frame,
            text="🔙 返回",
            command=self._on_back
        )
        back_btn.pack(side=tk.LEFT)

    def _select_all(self):
        """全选所有宽度"""
        for var in self.width_vars.values():
            var.set(True)
        self._update_selection()
    
    def _select_none(self):
        """取消选择所有宽度"""
        for var in self.width_vars.values():
            var.set(False)
        self._update_selection()
    
    def _on_width_changed(self):
        """宽度选择改变时的回调"""
        self._update_selection()
    
    def _update_selection(self):
        """更新选择状态"""
        self.selected_widths = {
            width for width, var in self.width_vars.items() 
            if var.get()
        }
        
        # 更新统计信息
        count = len(self.selected_widths)
        self.stats_label.config(text=f"已选择: {count} 个宽度")
    
    def _on_confirm(self):
        """确定按钮点击事件"""
        if not self.selected_widths:
            messagebox.showwarning("警告", "请至少选择一个宽度规格！")
            return
        
        self.result = {
            'confirmed': True,
            'selected_widths': list(self.selected_widths),
            'material': self.material,
            'length': self.length,
            'action': 'confirm'
        }
        
        logger.info(f"用户确认宽度选择: {len(self.selected_widths)}个宽度")
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消按钮点击事件"""
        self.result = {
            'confirmed': False,
            'selected_widths': [],
            'material': self.material,
            'length': self.length,
            'action': 'cancel'
        }

        logger.info("用户取消宽度选择")
        self.dialog.destroy()

    def _on_back(self):
        """返回按钮点击事件"""
        self.result = {
            'confirmed': False,
            'selected_widths': [],
            'material': self.material,
            'length': self.length,
            'action': 'back'
        }

        logger.info("用户选择返回到材质+长度选择")
        self.dialog.destroy()
    
    def show(self) -> Optional[Dict]:
        """
        显示对话框并等待用户操作
        
        Returns:
            用户选择结果，如果取消则返回None
        """
        # 等待对话框关闭
        self.dialog.wait_window()
        
        # 返回所有结果，包括返回操作
        return self.result


def show_width_selection_dialog(parent, available_widths: List[str], material: str, length: str) -> Optional[Dict]:
    """
    显示宽度选择对话框的便捷函数
    
    Args:
        parent: 父窗口
        available_widths: 可用的宽度列表
        material: 材质名称
        length: 长度信息
        
    Returns:
        用户选择结果，如果取消则返回None
    """
    dialog = WidthSelectionDialog(parent, available_widths, material, length)
    return dialog.show()


# 测试函数
def test_width_selection_dialog():
    """测试宽度选择对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 测试数据
    test_widths = [
        '1/4"*50 Yards',
        '1/2"*50 Yards', 
        '3/8"*50 Yards',
        '5/8"*50 Yards',
        '1"*50 Yards',
        '1-1/2"*50 Yards'
    ]
    
    result = show_width_selection_dialog(
        root, 
        test_widths, 
        "Grosgrain", 
        "50 Yards"
    )
    
    if result:
        print("✅ 用户确认选择:")
        print(f"   材质: {result['material']}")
        print(f"   长度: {result['length']}")
        print(f"   选择的宽度: {result['selected_widths']}")
    else:
        print("❌ 用户取消选择")
    
    root.destroy()


if __name__ == "__main__":
    test_width_selection_dialog()
