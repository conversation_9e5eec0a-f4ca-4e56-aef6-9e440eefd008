# 配置文件说明 📋

## 🔄 配置方式变更通知

**重要更新：** 程序已恢复使用JSON配置文件进行字段映射管理！

### 📁 当前配置文件

- **主配置文件：** `field_mappings.json` ✅ 
- **Excel配置文件：** `字段映射配置_已停用.xlsx` ❌ (已停用)

### 🎯 为什么改回JSON配置？

1. **🔧 更灵活** - JSON格式支持更复杂的配置结构
2. **⚡ 更高效** - 程序读取JSON文件速度更快
3. **🛠️ 更专业** - 开发者更容易维护和扩展
4. **📦 更轻量** - 不依赖Excel相关库

### 📝 如何修改配置？

#### 方法1：直接编辑JSON文件 (推荐)
```bash
# 使用文本编辑器打开配置文件
notepad data/config/field_mappings.json
```

#### 方法2：使用配置管理脚本
```bash
# 查看配置摘要
python scripts/config_manager.py summary

# 查看特定商城配置
python scripts/config_manager.py marketplace fba
python scripts/config_manager.py marketplace low_price

# 验证配置文件
python scripts/config_manager.py validate
```

### 🔍 JSON配置文件结构

```json
{
  "marketplaces": {
    "low_price": {
      "name": "低价商城",
      "field_mappings": {
        "partial_update": {
          "字段名": {
            "source": "数据来源",
            "description": "字段说明"
          }
        }
      },
      "default_values": {
        "parent": {},
        "child": {}
      }
    },
    "fba": {
      "name": "FBA商城",
      "field_mappings": {},
      "default_values": {}
    }
  }
}
```

### 🚀 配置修改示例

#### 修改品牌名称
```json
"brand_name": {
  "value": "YOUR_BRAND",  // 改成你的品牌名
  "description": "品牌名称"
}
```

#### 修改数据来源
```json
"product_description": {
  "source": "target_listing.品名",  // 从品名列获取
  "description": "产品描述"
}
```

#### 设置固定值
```json
"country_of_origin": {
  "value": "China",  // 固定值
  "description": "原产国"
}
```

### ⚠️ 注意事项

1. **备份重要** - 修改前请备份原文件
2. **语法正确** - JSON格式要求严格，注意逗号和引号
3. **重启生效** - 修改后需要重启程序才能生效
4. **验证配置** - 使用验证脚本检查配置是否正确

### 🆘 遇到问题？

1. **配置文件损坏** - 从备份恢复或重新生成
2. **语法错误** - 使用JSON验证工具检查
3. **字段不生效** - 检查字段名是否正确
4. **程序报错** - 查看日志文件获取详细信息

### 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 查看程序日志文件
2. 运行配置验证脚本
3. 检查JSON文件语法
4. 联系技术支持

---

**更新时间：** 2025-07-29  
**配置版本：** JSON v1.0.0
