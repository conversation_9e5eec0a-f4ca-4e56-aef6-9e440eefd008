"""
GUI主界面
亚马逊Listing创建工具的图形化用户界面
支持多种商城类型（低价商城、FBA商城等）
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from typing import Optional, Dict, Any
import pandas as pd
from loguru import logger

# 导入调试工具
try:
    from ..utils.debug_helper import log_exception, log_debug
except ImportError:
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from utils.debug_helper import log_exception, log_debug

# 导入核心逻辑
import sys
import os  # 确保os在全局作用域中可用

# 设置Python路径
current_dir = os.path.dirname(__file__)
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 导入模块
from core.template_filler import TemplateFiller
from marketplace.factory import MarketplaceFactory
from config.config_manager import ConfigManager
from config.settings import Settings
from gui.width_selection_dialog import show_width_selection_dialog
from gui.material_length_selection_dialog import show_material_length_selection_dialog
from utils.width_parser import SizeParser


class MainWindow:
    """主窗口类"""

    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.config_manager = ConfigManager()
        self.settings = Settings()
        self.template_filler = TemplateFiller()

        # 商城相关变量
        self.current_marketplace = None
        self.marketplace_var = tk.StringVar()
        self.available_marketplaces = MarketplaceFactory.get_available_marketplaces()

        # 文件路径变量
        self.target_file_path = tk.StringVar()
        self.product_file_path = tk.StringVar()
        self.template_file_path = tk.StringVar()

        # 其他变量
        self.keyword_var = tk.StringVar()
        self.update_mode_var = tk.StringVar()

        # 父体维度选择变量
        self.selected_dimension = "SKC"  # 默认使用SKC维度

        # 初始化默认商城
        self.initialize_marketplace()

        self.setup_ui()

    def initialize_marketplace(self):
        """初始化商城设置"""
        # 获取上次使用的商城类型
        last_marketplace = self.config_manager.get_user_config("last_marketplace", self.settings.DEFAULT_MARKETPLACE)

        # 设置当前商城
        if last_marketplace in self.available_marketplaces:
            self.marketplace_var.set(self.available_marketplaces[last_marketplace])
            self.current_marketplace = MarketplaceFactory.create_marketplace(last_marketplace)
        else:
            # 使用默认商城
            default_marketplace = self.settings.DEFAULT_MARKETPLACE
            self.marketplace_var.set(self.available_marketplaces[default_marketplace])
            self.current_marketplace = MarketplaceFactory.create_marketplace(default_marketplace)

        # 设置其他默认值
        self.update_mode_var.set("部分更新")  # 默认使用部分更新
        self.selected_dimension = self.current_marketplace.config.default_dimension

    def setup_ui(self):
        """设置用户界面"""
        self.root.title(self.settings.WINDOW_TITLE)
        self.root.geometry(self.settings.WINDOW_SIZE)
        self.root.minsize(*self.settings.MIN_WINDOW_SIZE)
        self.root.resizable(True, True)
        
        # 设置样式
        self.setup_styles()
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="8")  # 进一步减少主框架padding
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题（使用更小的字体）
        title_label = ttk.Label(main_frame, text=self.settings.APP_NAME,
                               font=("Microsoft YaHei", 12, "bold"))
        title_label.pack(pady=(0, 8))  # 进一步减少标题下方间距

        # 商城类型和关键词区域（合并到一行）
        self.create_compact_top_section(main_frame)

        # 文件选择区域
        self.create_file_selection_section(main_frame)

        # 操作按钮区域
        self.create_action_buttons_section(main_frame)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                          maximum=100, style="TProgressbar")
        self.progress_bar.pack(fill=tk.X, pady=(15, 10))
        
        # 状态显示区域
        self.create_status_section(main_frame)

        # 初始化界面显示
        self.update_marketplace_info()
        self.update_file_info_display()
        self.update_dimension_options()
        self.update_mode_options()

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        
        # 设置主题
        try:
            style.theme_use('vista')
        except:
            style.theme_use('default')
        
        # 标题样式
        style.configure("Title.TLabel", 
                       font=("Microsoft YaHei", 18, "bold"),
                       foreground="#2E86AB")
        
        # 章节标题样式
        style.configure("Section.TLabel", 
                       font=("Microsoft YaHei", 12, "bold"),
                       foreground="#333333")
        
        # 按钮样式
        style.configure("Action.TButton", 
                       font=("Microsoft YaHei", 11),
                       padding=(20, 8))
        
        # 重置按钮样式
        style.configure("Reset.TButton",
                       font=("Microsoft YaHei", 10),
                       padding=(15, 8))

        # 紧凑按钮样式
        style.configure("Compact.TButton",
                       font=("Microsoft YaHei", 8),
                       padding=(8, 4))

    def create_compact_top_section(self, parent):
        """创建紧凑的顶部区域（商城类型和关键词在一行）"""
        # 创建框架
        top_frame = ttk.LabelFrame(parent, text="🏪 基本配置", padding="6")
        top_frame.pack(fill=tk.X, pady=(0, 6))

        # 第一行：商城类型和关键词
        row1_frame = ttk.Frame(top_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 3))

        # 商城类型
        ttk.Label(row1_frame, text="商城:",
                 font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))

        marketplace_options = list(self.available_marketplaces.values())
        self.marketplace_combo = ttk.Combobox(row1_frame,
                                            textvariable=self.marketplace_var,
                                            values=marketplace_options,
                                            state="readonly",
                                            font=("Microsoft YaHei", 9),
                                            width=15)
        self.marketplace_combo.pack(side=tk.LEFT, padx=(0, 15))

        # 关键词
        ttk.Label(row1_frame, text="关键词:",
                 font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))

        keyword_entry = ttk.Entry(row1_frame, textvariable=self.keyword_var,
                                 font=("Microsoft YaHei", 9), width=25)
        keyword_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        # 示例文本
        ttk.Label(row1_frame, text="如: satin ribbon",
                 font=("Microsoft YaHei", 8), foreground="gray").pack(side=tk.RIGHT)

        # 绑定选择事件
        self.marketplace_combo.bind('<<ComboboxSelected>>', self.on_marketplace_changed)

    def create_marketplace_section(self, parent):
        """创建商城类型选择区域"""
        # 创建框架
        marketplace_frame = ttk.LabelFrame(parent, text="🏪 商城类型", padding="10")  # 减少padding
        marketplace_frame.pack(fill=tk.X, pady=(0, 10))  # 减少间距

        # 选择区域
        selection_frame = ttk.Frame(marketplace_frame)
        selection_frame.pack(fill=tk.X)

        # 标签
        ttk.Label(selection_frame, text="选择商城:",
                 font=("Microsoft YaHei", 11, "bold")).pack(side=tk.LEFT, padx=(0, 10))

        # 商城选择下拉框
        marketplace_options = list(self.available_marketplaces.values())
        self.marketplace_combo = ttk.Combobox(selection_frame,
                                            textvariable=self.marketplace_var,
                                            values=marketplace_options,
                                            state="readonly",
                                            font=("Microsoft YaHei", 10),
                                            width=30)
        self.marketplace_combo.pack(side=tk.LEFT, padx=(0, 15))

        # 绑定选择事件
        self.marketplace_combo.bind('<<ComboboxSelected>>', self.on_marketplace_changed)

        # 商城信息显示
        self.marketplace_info_label = ttk.Label(selection_frame,
                                              text=self.get_marketplace_info_text(),
                                              foreground="blue",
                                              font=("Microsoft YaHei", 9))
        self.marketplace_info_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

    def create_keyword_section(self, parent):
        """创建商品关键词输入区域"""
        # 创建框架
        keyword_frame = ttk.LabelFrame(parent, text="🔍 商品关键词", padding="10")  # 减少padding
        keyword_frame.pack(fill=tk.X, pady=(0, 10))  # 减少间距
        
        # 输入框架
        input_frame = ttk.Frame(keyword_frame)
        input_frame.pack(fill=tk.X)
        
        # 标签
        ttk.Label(input_frame, text="关键词:", 
                 font=("Microsoft YaHei", 10)).pack(side=tk.LEFT, padx=(0, 10))
        
        # 输入框
        keyword_entry = ttk.Entry(input_frame, textvariable=self.keyword_var, 
                                 font=("Microsoft YaHei", 10), width=50)
        keyword_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        
        # 示例文本
        ttk.Label(input_frame, text="示例: satin ribbon", 
                 foreground="gray", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)
        
    def create_file_selection_section(self, parent):
        """创建文件选择区域"""
        # 创建框架
        files_frame = ttk.LabelFrame(parent, text="📁 文件配置", padding="6")
        files_frame.pack(fill=tk.X, pady=(0, 6))

        # 第一行：目标文件和更新模式
        row1_frame = ttk.Frame(files_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 3))

        # 目标文件
        ttk.Label(row1_frame, text="目标文件:",
                 font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))

        target_entry = ttk.Entry(row1_frame, textvariable=self.target_file_path,
                                state="readonly", font=("Microsoft YaHei", 8), width=20)
        target_entry.pack(side=tk.LEFT, padx=(0, 3))

        ttk.Button(row1_frame, text="选择",
                  command=lambda: self.select_file(self.target_file_path, "target_files", "Excel文件", "*.xlsx"),
                  width=6).pack(side=tk.LEFT, padx=(0, 10))

        # 更新模式
        ttk.Label(row1_frame, text="模式:",
                 font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))

        self.update_mode_combo = ttk.Combobox(row1_frame,
                                            textvariable=self.update_mode_var,
                                            values=self.get_current_update_modes(),
                                            state="readonly",
                                            font=("Microsoft YaHei", 8),
                                            width=15)
        self.update_mode_combo.pack(side=tk.LEFT)

        # 绑定选择事件，防止选择不支持的模式
        self.update_mode_combo.bind('<<ComboboxSelected>>', self.on_update_mode_changed)

        # 第二行：其他文件（可折叠）
        self.show_advanced_files = tk.BooleanVar(value=False)

        # 高级选项切换按钮
        advanced_btn = ttk.Checkbutton(files_frame, text="显示高级文件选项",
                                      variable=self.show_advanced_files,
                                      command=self.toggle_advanced_files)
        advanced_btn.pack(anchor=tk.W, pady=(3, 0))

        # 高级文件选择区域（默认隐藏）
        self.advanced_files_frame = ttk.Frame(files_frame)

        # 产品资料和模板文件
        row2_frame = ttk.Frame(self.advanced_files_frame)
        row2_frame.pack(fill=tk.X, pady=(3, 0))

        # 产品资料文件
        ttk.Label(row2_frame, text="产品资料:",
                 font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))

        product_entry = ttk.Entry(row2_frame, textvariable=self.product_file_path,
                                 state="readonly", font=("Microsoft YaHei", 8), width=15)
        product_entry.pack(side=tk.LEFT, padx=(0, 3))

        ttk.Button(row2_frame, text="选择",
                  command=lambda: self.select_file(self.product_file_path, "product_files", "Excel文件", "*.xlsx"),
                  width=6).pack(side=tk.LEFT, padx=(0, 10))

        # 模板文件
        ttk.Label(row2_frame, text="模板:",
                 font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))

        template_entry = ttk.Entry(row2_frame, textvariable=self.template_file_path,
                                  state="readonly", font=("Microsoft YaHei", 8), width=15)
        template_entry.pack(side=tk.LEFT, padx=(0, 3))

        ttk.Button(row2_frame, text="选择",
                  command=lambda: self.select_file(self.template_file_path, "template_files", "Excel宏文件", "*.xlsm"),
                  width=6).pack(side=tk.LEFT)
        
    def create_action_buttons_section(self, parent):
        """创建操作按钮区域"""
        # 父体维度选择框架
        dimension_frame = ttk.LabelFrame(parent, text="🎯 父体维度配置", padding="10")  # 减少padding
        dimension_frame.pack(fill=tk.X, pady=(0, 15))  # 减少间距
        
        # 维度选择区域
        selection_area = ttk.Frame(dimension_frame)
        selection_area.pack(fill=tk.X)
        
        # 标签
        dimension_label = ttk.Label(selection_area, text="父体维度:", 
                                   font=("Microsoft YaHei", 11, "bold"))
        dimension_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 下拉选择框
        self.dimension_var = tk.StringVar(value="SKC维度（同材质+同宽度+同长度）")
        dimension_options = [
            "SKC维度（同材质+同宽度+同长度）",
            "材质+宽度维度（同材质+同宽度+不同长度）[未来支持]",
            "自定义维度（自定义分组规则）[未来支持]"
        ]
        
        self.dimension_combo = ttk.Combobox(selection_area, 
                                          textvariable=self.dimension_var,
                                          values=dimension_options,
                                          state="readonly",
                                          font=("Microsoft YaHei", 10),
                                          width=45)
        self.dimension_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 描述区域
        desc_frame = ttk.Frame(dimension_frame)
        desc_frame.pack(fill=tk.X, pady=(10, 0))
        
        desc_label = ttk.Label(desc_frame, text="维度说明:", 
                              font=("Microsoft YaHei", 10, "bold"))
        desc_label.pack(anchor=tk.W)
        
        self.desc_text = tk.Text(desc_frame, height=3, wrap=tk.WORD,  # 减少高度
                                font=("Microsoft YaHei", 9),
                                bg="#f8f9fa", fg="#333333",
                                relief=tk.FLAT, bd=1,
                                state=tk.DISABLED)
        self.desc_text.pack(fill=tk.X, pady=(5, 0))
        
        # 绑定下拉框选择事件
        self.dimension_combo.bind('<<ComboboxSelected>>', self.update_dimension_description)
        
        # 初始化描述
        self.update_dimension_description()
        
        # 操作按钮（移到维度选择同一行）
        button_row = ttk.Frame(dimension_frame)
        button_row.pack(fill=tk.X, pady=(8, 0))

        # 生成按钮
        self.generate_btn = ttk.Button(button_row, text="🚀 生成模板",
                                      command=self.start_generation_with_validation,
                                      width=12)
        self.generate_btn.pack(side=tk.LEFT, padx=(0, 8))

        # 重置按钮
        reset_btn = ttk.Button(button_row, text="🔄 重置",
                              command=self.reset_form,
                              width=8)
        reset_btn.pack(side=tk.LEFT)
        
    def create_status_section(self, parent):
        """创建状态显示区域"""
        # 状态框架
        status_frame = ttk.LabelFrame(parent, text="📋 状态信息", padding="8")  # 减少padding
        status_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文本框和滚动条的容器
        text_container = ttk.Frame(status_frame)
        text_container.pack(fill=tk.BOTH, expand=True)
        
        # 文本框
        self.status_text = tk.Text(text_container, height=4, wrap=tk.WORD,  # 进一步减少高度
                                  font=("Consolas", 8),  # 减小字体
                                  bg="#f8f9fa", fg="#333333",
                                  relief=tk.FLAT, bd=1)
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        status_scrollbar = ttk.Scrollbar(text_container, orient=tk.VERTICAL, 
                                        command=self.status_text.yview)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)
        
        # 设置文本框为只读
        self.status_text.configure(state=tk.DISABLED)

    def toggle_advanced_files(self):
        """切换高级文件选项的显示/隐藏"""
        if self.show_advanced_files.get():
            self.advanced_files_frame.pack(fill=tk.X, pady=(3, 0))
        else:
            self.advanced_files_frame.pack_forget()

    def toggle_description(self):
        """切换维度描述的显示/隐藏"""
        if self.show_description.get():
            self.desc_frame.pack(fill=tk.X, pady=(3, 0))
        else:
            self.desc_frame.pack_forget()

    def on_update_mode_changed(self, event=None):
        """更新模式改变时的处理"""
        selected_mode = self.update_mode_var.get()

        # 如果选择了不支持的模式，显示提示并重置为部分更新
        if "暂不支持" in selected_mode:
            messagebox.showwarning(
                "功能提示",
                "完全更新功能暂未实现，当前只支持部分更新模式。\n\n"
                "部分更新模式已能满足大部分使用需求。"
            )
            # 重置为部分更新
            self.update_mode_var.set("部分更新")

    def on_marketplace_changed(self, event=None):
        """商城类型改变时的处理"""
        selected_display_name = self.marketplace_var.get()

        # 找到对应的商城类型代码
        marketplace_type = None
        for code, display_name in self.available_marketplaces.items():
            if display_name == selected_display_name:
                marketplace_type = code
                break

        if marketplace_type:
            # 更新当前商城
            self.current_marketplace = MarketplaceFactory.create_marketplace(marketplace_type)

            # 更新界面显示
            self.update_marketplace_info()
            self.update_file_info_display()
            self.update_dimension_options()
            self.update_mode_options()

            # 保存用户选择
            self.config_manager.set_user_config("last_marketplace", marketplace_type)

            self.log_message(f"🏪 已切换到: {selected_display_name}")

    def get_marketplace_info_text(self) -> str:
        """获取商城信息文本"""
        if self.current_marketplace:
            return f"当前商城: {self.current_marketplace.config.display_name}"
        return "未选择商城"

    def update_marketplace_info(self):
        """更新商城信息显示"""
        if hasattr(self, 'marketplace_info_label'):
            self.marketplace_info_label.config(text=self.get_marketplace_info_text())

    def update_file_info_display(self):
        """更新文件信息显示"""
        if not hasattr(self, 'file_info_frame') or not self.current_marketplace:
            return

        # 清除现有的信息显示
        for widget in self.file_info_frame.winfo_children():
            widget.destroy()

        # 显示当前商城的默认文件信息
        config = self.current_marketplace.config

        info_items = [
            ("📁 目标文件", config.target_file),
            ("📊 产品资料", config.product_file),
            ("📋 模板文件", config.template_file)
        ]

        for i, (label, file_path) in enumerate(info_items):
            frame = ttk.Frame(self.file_info_frame)
            frame.pack(fill=tk.X, pady=2)

            ttk.Label(frame, text="💡", foreground="blue",
                     font=("Microsoft YaHei", 10)).pack(side=tk.LEFT, padx=(0, 5))

            filename = os.path.basename(file_path) if file_path else "未设置"
            ttk.Label(frame, text=f"{label}: {filename} (默认)",
                     foreground="blue", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)

    def get_current_update_modes(self) -> list:
        """获取当前商城支持的更新模式"""
        if self.current_marketplace:
            modes = self.current_marketplace.config.update_modes
            mode_list = []
            for mode in modes:
                if mode == "partial":
                    mode_list.append("部分更新")
                elif mode == "full":
                    mode_list.append("完全更新 (暂不支持)")
                else:
                    mode_list.append(mode.title() + " Update")
            return mode_list
        return ["部分更新"]

    def update_mode_options(self):
        """更新模式选项"""
        if hasattr(self, 'update_mode_combo'):
            modes = self.get_current_update_modes()
            self.update_mode_combo['values'] = modes
            # 默认选择部分更新（第一个选项）
            if modes:
                # 确保默认选择部分更新
                partial_mode = next((mode for mode in modes if "部分更新" in mode), modes[0])
                self.update_mode_var.set(partial_mode)

    def update_dimension_options(self):
        """更新维度选项"""
        if hasattr(self, 'dimension_combo') and self.current_marketplace:
            dimensions = self.current_marketplace.config.supported_dimensions
            dimension_options = []

            for dim in dimensions:
                if dim == "SKC":
                    dimension_options.append("SKC维度（同材质+同宽度+同长度）")
                elif dim == "MATERIAL_WIDTH":
                    dimension_options.append("材质+宽度维度（同材质+同宽度+不同长度）")
                elif dim == "MATERIAL_LENGTH":
                    dimension_options.append("材质+长度维度（同材质+同长度+不同宽度）")
                elif dim == "VARIANT_ATTRIBUTES":
                    dimension_options.append("变体属性维度（基于[size:xxx];[color:xxx]格式）")
                else:
                    dimension_options.append(f"{dim}维度")

            self.dimension_combo['values'] = dimension_options
            if dimension_options:
                self.dimension_var.set(dimension_options[0])
                self.selected_dimension = dimensions[0]

    def select_file(self, var: tk.StringVar, file_type: str, description: str, pattern: str):
        """
        选择文件

        Args:
            var: 存储文件路径的变量
            file_type: 文件类型（用于记录最近文件）
            description: 文件描述
            pattern: 文件格式模式
        """
        # 获取初始目录（优先使用最近文件的目录）
        recent_files = self.config_manager.get_recent_files(file_type)
        initial_dir = self.settings.INPUT_DIR
        if recent_files and os.path.exists(recent_files[0]):
            initial_dir = os.path.dirname(recent_files[0])

        filename = filedialog.askopenfilename(
            title=f"选择{description}",
            initialdir=initial_dir,
            filetypes=[(description, pattern), ("所有文件", "*.*")]
        )
        if filename:
            var.set(filename)
            # 记录到最近文件
            self.config_manager.add_recent_file(file_type, filename)
            self.log_message(f"📁 已选择{description}: {os.path.basename(filename)}")
    
    def log_message(self, message: str):
        """
        记录消息到状态文本框
        
        Args:
            message: 要记录的消息
        """
        self.status_text.configure(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.configure(state=tk.DISABLED)
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def reset_form(self):
        """重置表单"""
        self.keyword_var.set("")
        self.target_file_path.set("")
        self.product_file_path.set("")
        self.template_file_path.set("")
        self.progress_var.set(0)

        # 重置为默认值
        if self.current_marketplace:
            self.update_mode_var.set("部分更新")  # 重置为部分更新
            self.selected_dimension = self.current_marketplace.config.default_dimension
            self.update_dimension_options()

        # 清空状态文本
        self.status_text.configure(state=tk.NORMAL)
        self.status_text.delete(1.0, tk.END)
        self.status_text.configure(state=tk.DISABLED)

        self.log_message("✅ 表单已重置")
    
    def validate_input(self) -> bool:
        """
        验证输入
        
        Returns:
            是否验证通过
        """
        keyword = self.keyword_var.get().strip()
        if not keyword:
            messagebox.showerror("输入错误", "请输入商品关键词！")
            return False
        
        return True
    
    def update_dimension_description(self, event=None):
        """更新维度描述"""
        selected_text = self.dimension_var.get()
        descriptions = {
            "SKC维度（同材质+同宽度+同长度）":
                "🎯 SKC维度特点：完全相同规格的产品归为一个父体，材质、宽度、长度必须完全一致。适用于完全标准化的产品系列，当前项目默认维度，已完全支持。",

            "材质+宽度维度（同材质+同宽度+不同长度）":
                "🔄 材质+宽度维度特点：相同材质和宽度，但长度可以不同。更灵活的产品分组方式，适用于长度规格较多的产品系列。",

            "材质+长度维度（同材质+同长度+不同宽度）":
                "🎨 材质+长度维度特点：相同材质和长度，但宽度可以不同。适用于宽度规格较多的产品系列，用户可以选择包含哪些宽度规格。",

            "变体属性维度（基于[size:xxx];[color:xxx]格式）":
                "🏷️ 变体属性维度特点：自动识别表格中的变体属性字段（如[size:1-1/2\" X 50 Yards];[color:Ivory]格式），按尺寸分组创建父子体结构。适用于包含变体属性格式数据的表格，新增功能。",

            "自定义维度（自定义分组规则）[未来支持]":
                "⚙️ 自定义维度特点：根据用户自定义字段组合进行分组，高度灵活的父体创建规则，支持复杂的业务分组需求。功能正在规划中，敬请期待。"
        }
        
        description = descriptions.get(selected_text, "请选择一个维度查看详细说明")
        
        self.desc_text.configure(state=tk.NORMAL)
        self.desc_text.delete('1.0', tk.END)
        self.desc_text.insert('1.0', description)
        self.desc_text.configure(state=tk.DISABLED)
        
    def start_generation_with_validation(self):
        """验证维度选择并开始生成"""
        if not self.validate_input():
            return
            
        # 检查选择的维度
        selected_text = self.dimension_var.get()
        if "[未来支持]" in selected_text:
            messagebox.showwarning("功能暂未支持", 
                                 f"抱歉，{selected_text}功能正在开发中，敬请期待！\n\n"
                                 "当前版本仅支持SKC维度，请选择SKC维度继续。")
            return
            
        # 设置内部维度代码
        if "SKC维度" in selected_text:
            self.selected_dimension = "SKC"
        elif "材质+宽度维度" in selected_text:
            self.selected_dimension = "MATERIAL_WIDTH"
        elif "材质+长度维度" in selected_text:
            self.selected_dimension = "MATERIAL_LENGTH"
        elif "变体属性维度" in selected_text:
            self.selected_dimension = "VARIANT_ATTRIBUTES"
        elif "自定义维度" in selected_text:
            self.selected_dimension = "CUSTOM"
        else:
            self.selected_dimension = "SKC"  # 默认

        self.log_message(f"🎯 已选择父体维度: {self.get_dimension_name(self.selected_dimension)}")

        # 如果选择的是材质+长度维度，需要先让用户选择宽度
        if self.selected_dimension == "MATERIAL_LENGTH":
            self.handle_material_length_dimension()
        else:
            self.start_generation()

    def handle_material_length_dimension(self):
        """处理材质+长度维度的宽度选择"""
        try:
            # 获取目标listing文件路径
            target_file = self.target_file_path.get() if self.target_file_path.get() else None
            if not target_file:
                # 使用默认文件
                target_file = self.current_marketplace.config.target_file

            # 读取目标listing数据
            if not os.path.exists(target_file):
                messagebox.showerror("文件错误", f"目标listing文件不存在: {target_file}")
                return

            self.log_message("📊 正在分析可用宽度规格...")

            # 读取Excel文件
            try:
                df = pd.read_excel(target_file)
                log_debug(f"成功读取目标文件", {"文件路径": target_file, "数据行数": len(df)})
            except Exception as read_error:
                log_exception(read_error, f"读取目标文件失败 - 文件: {target_file}")
                raise

            # 获取所有可用的宽度
            available_widths = set()
            material_length_groups = {}

            # 添加调试计数器
            total_rows = len(df)
            processed_rows = 0
            skipped_no_size = 0
            skipped_no_length = 0
            skipped_no_width = 0
            valid_rows = 0

            log_debug("开始处理数据行", {"总行数": total_rows, "数据列名": list(df.columns)})

            for index, row in df.iterrows():
                processed_rows += 1

                # 尝试从多个可能的列中获取尺寸信息
                size = ''
                product_description = ''

                # 优先从size列获取
                if 'size' in df.columns:
                    size = row.get('size', '')
                    if processed_rows <= 5:
                        log_debug(f"第{index+1}行从size列获取尺寸", {"尺寸": size})

                # 只有当存在变体属性列且size列为空时，才从变体属性中提取
                elif '变体属性' in df.columns:
                    variant_attrs = row.get('变体属性', '')
                    if variant_attrs and isinstance(variant_attrs, str):
                        # 解析变体属性，提取size信息
                        import re
                        size_match = re.search(r'\[size:([^\]]+)\]', variant_attrs)
                        if size_match:
                            size = size_match.group(1).strip()
                            if processed_rows <= 5:
                                log_debug(f"第{index+1}行从变体属性提取尺寸", {
                                    "变体属性": variant_attrs,
                                    "提取的尺寸": size
                                })

                # 尝试从多个可能的列获取产品描述
                for desc_col in ['Product Description', '标题', '品名']:
                    if desc_col in df.columns:
                        desc_value = row.get(desc_col, '')
                        if desc_value and not pd.isna(desc_value):
                            product_description = str(desc_value)
                            if processed_rows <= 5:
                                log_debug(f"第{index+1}行从{desc_col}列获取产品描述", {
                                    "描述长度": len(product_description)
                                })
                            break

                if pd.isna(size) or size == '':
                    skipped_no_size += 1
                    if processed_rows <= 5:  # 只显示前5行的详细信息
                        log_debug(f"跳过第{index+1}行：无尺寸信息", {
                            "变体属性": row.get('变体属性', ''),
                            "提取的尺寸": size,
                            "行数据": dict(row)
                        })
                    continue

                # 记录成功提取的尺寸信息
                if processed_rows <= 5:
                    log_debug(f"第{index+1}行成功提取尺寸", {
                        "变体属性": row.get('变体属性', ''),
                        "提取的尺寸": size,
                        "产品描述": product_description[:100] + "..." if len(product_description) > 100 else product_description
                    })

                # 解析尺寸信息
                size_components = SizeParser.parse_size_components(str(size))
                if processed_rows <= 5:  # 只显示前5行的详细信息
                    log_debug(f"第{index+1}行尺寸解析", {
                        "原始尺寸": str(size),
                        "解析结果": size_components
                    })

                # 解析材质信息
                material = SizeParser.parse_material(str(product_description), str(size))
                if not material:
                    material = "UNKNOWN"

                # 获取长度信息
                length_info = size_components.get('length_info')
                if not length_info:
                    skipped_no_length += 1
                    if processed_rows <= 5:  # 只显示前5行的详细信息
                        log_debug(f"跳过第{index+1}行：无长度信息", {
                            "尺寸": str(size),
                            "解析结果": size_components
                        })
                    continue

                # 获取宽度信息
                width_info = size_components.get('width_info')
                if not width_info:
                    skipped_no_width += 1
                    if processed_rows <= 5:  # 只显示前5行的详细信息
                        log_debug(f"跳过第{index+1}行：无宽度信息", {
                            "尺寸": str(size),
                            "解析结果": size_components
                        })
                    continue

                # 生成材质+长度组合键
                length_display = length_info['length_display']
                group_key = f"{material}-{length_display}"

                valid_rows += 1
                if valid_rows <= 5:  # 只显示前5行的详细信息
                    log_debug(f"第{index+1}行有效数据", {
                        "材质": material,
                        "长度": length_display,
                        "组合键": group_key,
                        "尺寸": str(size)
                    })

                if group_key not in material_length_groups:
                    material_length_groups[group_key] = {
                        'material': material,
                        'length': length_display,
                        'widths': set()
                    }

                material_length_groups[group_key]['widths'].add(str(size))
                available_widths.add(str(size))

            # 输出处理统计
            log_debug("数据处理统计", {
                "总行数": total_rows,
                "已处理": processed_rows,
                "跳过(无尺寸)": skipped_no_size,
                "跳过(无长度)": skipped_no_length,
                "跳过(无宽度)": skipped_no_width,
                "有效行数": valid_rows,
                "找到的组合数": len(material_length_groups),
                "组合列表": list(material_length_groups.keys()) if material_length_groups else []
            })

            if not material_length_groups:
                # 输出详细的调试信息
                log_debug("未找到材质+长度组合的详细分析", {
                    "目标文件": target_file,
                    "数据行数": len(df),
                    "数据列名": list(df.columns),
                    "前5行数据": df.head().to_dict() if not df.empty else "数据为空"
                })

                # 检查关键列是否存在
                required_columns = ['material', 'length', 'size']
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    error_msg = f"数据文件缺少必要的列: {missing_columns}"
                    log_debug("缺少必要列", {"缺少的列": missing_columns, "现有列": list(df.columns)})
                else:
                    # 检查数据内容
                    material_values = df['material'].dropna().unique() if 'material' in df.columns else []
                    length_values = df['length'].dropna().unique() if 'length' in df.columns else []
                    size_values = df['size'].dropna().unique() if 'size' in df.columns else []

                    log_debug("数据内容分析", {
                        "材质值": list(material_values),
                        "长度值": list(length_values),
                        "尺寸值": list(size_values),
                        "材质数量": len(material_values),
                        "长度数量": len(length_values),
                        "尺寸数量": len(size_values)
                    })

                    error_msg = "数据格式正确但未找到有效的材质+长度组合"

                self.log_message(f"❌ {error_msg}")
                messagebox.showwarning("数据错误", f"未找到可用的材质+长度组合，请检查数据格式。\n\n详细信息已输出到终端，请查看调试信息。")
                return

            # 如果只有一个材质+长度组合，直接显示宽度选择
            if len(material_length_groups) == 1:
                group_key, group_info = next(iter(material_length_groups.items()))
                result = self.show_width_selection_for_group(group_info)
                if result == 'back':
                    self.log_message("🔙 用户选择返回到维度选择")
                    self.log_message("💡 请在主界面重新选择父体维度配置")
            else:
                # 多个材质+长度组合，让用户选择
                self.show_material_length_selection(material_length_groups)

        except Exception as e:
            error_msg = f"分析宽度规格时出错: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            log_exception(e, "分析宽度规格时出错")
            messagebox.showerror("分析错误", error_msg)

    def show_width_selection_for_group(self, group_info: Dict[str, Any]):
        """为指定的材质+长度组合显示宽度选择对话框"""
        try:
            material = group_info['material']
            length = group_info['length']
            available_widths = list(group_info['widths'])

            self.log_message(f"🎨 材质: {material}, 长度: {length}")
            self.log_message(f"📐 发现 {len(available_widths)} 个宽度规格")
            self.log_message(f"📋 宽度规格列表: {', '.join(available_widths)}")

            # 显示宽度选择对话框
            self.log_message("🔄 正在显示宽度选择对话框...")
            result = show_width_selection_dialog(
                self.root,
                available_widths,
                material,
                length
            )

            self.log_message(f"📊 宽度选择对话框返回结果: {result}")

            if result and result['confirmed']:
                selected_widths = result['selected_widths']
                self.log_message(f"✅ 用户选择了 {len(selected_widths)} 个宽度规格")

                # 保存用户选择的宽度信息
                self.selected_width_info = {
                    'material': material,
                    'length': length,
                    'selected_widths': selected_widths
                }

                # 开始生成模板
                self.start_generation()
            elif result and result.get('action') == 'back':
                self.log_message("🔙 用户选择返回到材质+长度选择")
                # 返回到材质+长度选择对话框，需要传递已有的数据
                return 'back'
            else:
                self.log_message("❌ 用户取消了宽度选择")

        except Exception as e:
            error_msg = f"显示宽度选择对话框时出错: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            log_exception(e, "显示宽度选择对话框时出错")
            messagebox.showerror("错误", error_msg)

    def show_material_length_selection(self, material_length_groups: Dict[str, Dict]):
        """显示材质+长度组合选择（多个组合时使用）"""
        self.log_message(f"🔍 发现 {len(material_length_groups)} 个材质+长度组合")

        # 显示材质+长度选择对话框
        result = show_material_length_selection_dialog(
            self.root,
            material_length_groups
        )

        if result and result['confirmed']:
            selected_group = result['selected_group']
            selected_group_key = result['selected_group_key']

            self.log_message(f"✅ 用户选择了组合: {selected_group_key}")
            self.log_message(f"🎨 材质: {selected_group['material']}")
            self.log_message(f"📏 长度: {selected_group['length']}")
            self.log_message(f"📐 包含 {len(selected_group['widths'])} 个宽度规格")

            # 显示宽度选择对话框，如果返回则重新显示材质+长度选择
            while True:
                width_result = self.show_width_selection_for_group(selected_group)
                if width_result != 'back':
                    break
                # 如果用户选择返回，重新显示材质+长度选择对话框
                self.log_message("🔄 重新显示材质+长度选择对话框")
                new_result = show_material_length_selection_dialog(
                    self.root,
                    material_length_groups
                )
                if new_result and new_result['confirmed']:
                    selected_group = new_result['selected_group']
                    selected_group_key = new_result['selected_group_key']
                    self.log_message(f"✅ 用户重新选择了组合: {selected_group_key}")
                else:
                    # 用户取消或返回，退出循环
                    break
        elif result and result.get('action') == 'back':
            self.log_message("🔙 用户选择返回到维度选择")
            self.log_message("💡 请在主界面重新选择父体维度配置")
        else:
            self.log_message("❌ 用户取消了材质+长度组合选择")

    def start_generation(self):
        """开始生成模板（在后台线程中执行）"""
        if not self.validate_input():
            return
        
        # 禁用生成按钮
        self.generate_btn.configure(state=tk.DISABLED, text="🔄 生成中...")
        self.progress_var.set(0)
        
        # 在后台线程中执行生成
        thread = threading.Thread(target=self.generate_template_background)
        thread.daemon = True
        thread.start()
    
    def generate_template_background(self):
        """在后台线程中生成模板"""
        try:
            keyword = self.keyword_var.get().strip()
            log_debug("开始生成模板", {"keyword": keyword})
            print(f"🔧 调试: 开始生成模板，关键词: {keyword}")  # 强制输出到终端

            # 获取文件路径（只有目标listing文件是可选的）
            target_file = self.target_file_path.get() if self.target_file_path.get() else None
            
            self.log_message("🚀 开始生成模板...")
            self.progress_var.set(20)
            
            # 获取用户选择的文件路径
            target_file = self.target_file_path.get() if self.target_file_path.get() else None
            product_file = self.product_file_path.get() if self.product_file_path.get() else None
            template_file = self.template_file_path.get() if self.template_file_path.get() else None

            # 获取更新模式
            update_mode_text = self.update_mode_var.get()
            # 目前只支持部分更新
            update_mode = 'partial'

            self.log_message(f"📝 商品关键词: {keyword}")
            self.log_message(f"🏪 商城类型: {self.current_marketplace.config.display_name}")

            if target_file:
                self.log_message(f"📁 目标Listing文件: {os.path.basename(target_file)}")
            else:
                self.log_message(f"📁 目标Listing文件: 使用默认文件")

            if product_file:
                self.log_message(f"📊 产品资料文件: {os.path.basename(product_file)}")
            else:
                self.log_message(f"📊 产品资料文件: 使用默认文件")

            if template_file:
                self.log_message(f"📋 模板文件: {os.path.basename(template_file)}")
            else:
                self.log_message(f"📋 模板文件: 使用默认文件")

            self.log_message(f"⚙️ 更新模式: {update_mode_text}")
            self.log_message(f"🎯 父体维度: {self.get_dimension_name(self.selected_dimension)}")
            self.progress_var.set(40)

            # 执行模板填充
            width_info = getattr(self, 'selected_width_info', None)
            log_debug("开始执行模板填充", {
                "keyword": keyword,
                "target_file": target_file,
                "product_file": product_file,
                "template_file": template_file,
                "update_mode": update_mode,
                "dimension": self.selected_dimension,
                "width_info": width_info
            })

            success, message = self.template_filler.process_listing_creation(
                keyword=keyword,
                target_file=target_file,
                product_file=product_file,
                template_file=template_file,
                update_mode=update_mode,
                marketplace=self.current_marketplace,
                dimension=self.selected_dimension,
                width_selection_info=width_info
            )

            log_debug("模板填充完成", {"success": success, "message": message})
            
            self.progress_var.set(100)
            
            if success:
                self.log_message("=" * 50)
                self.log_message(message)
                self.log_message("=" * 50)
                messagebox.showinfo("生成成功", "✅ 模板生成成功！\n\n请查看 data/output/ 目录中的结果文件。")
            else:
                self.log_message("=" * 50)
                self.log_message(f"❌ 生成失败: {message}")
                self.log_message("=" * 50)
                messagebox.showerror("生成失败", f"❌ 模板生成失败:\n\n{message}")
                
        except Exception as e:
            error_msg = f"❌ 生成过程中出现异常: {str(e)}"
            self.log_message("=" * 50)
            self.log_message(error_msg)
            self.log_message("=" * 50)
            log_exception(e, "生成过程中出现异常")
            messagebox.showerror("系统错误", error_msg)
            
        finally:
            # 重新启用生成按钮
            self.root.after(0, self.enable_generate_button)
    
    def enable_generate_button(self):
        """重新启用生成按钮"""
        self.generate_btn.configure(state=tk.NORMAL, text="🚀 生成Listing模板")
    

    
    def get_dimension_name(self, dimension_code):
        """获取维度的中文名称"""
        dimension_names = {
            "SKC": "SKC维度（同材质+同宽度+同长度）",
            "MATERIAL_WIDTH": "材质+宽度维度（同材质+同宽度+不同长度）",
            "MATERIAL_LENGTH": "材质+长度维度（同材质+同长度+不同宽度）",
            "VARIANT_ATTRIBUTES": "变体属性维度（基于[size:xxx];[color:xxx]格式）",
            "CUSTOM": "自定义维度"
        }
        return dimension_names.get(dimension_code, dimension_code)

    def run(self):
        """运行应用程序"""
        # 显示欢迎信息
        self.log_message(f"🏪 {self.settings.APP_NAME}已启动")
        self.log_message("=" * 60)
        self.log_message("💡 使用说明:")
        self.log_message("   1. 选择商城类型（低价商城/FBA商城）")
        self.log_message("   2. 输入商品关键词（必填）")
        self.log_message("   3. 可选择自定义文件（留空使用默认）")
        self.log_message("   4. 选择更新模式（目前仅支持部分更新）和父体维度")
        self.log_message("   5. 点击'🚀 生成Listing模板'开始生成")
        self.log_message("   6. 查看 data/output/ 目录中的结果")
        self.log_message("=" * 60)
        self.log_message("📋 当前配置:")
        if self.current_marketplace:
            config = self.current_marketplace.config
            self.log_message(f"   • 商城类型: {config.display_name}")
            self.log_message(f"   • 默认模板: {os.path.basename(config.template_file)}")
            self.log_message(f"   • 默认产品资料: {os.path.basename(config.product_file)}")
            self.log_message(f"   • 支持维度: {', '.join(config.supported_dimensions)}")
        self.log_message("=" * 60)

        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = MainWindow()
        app.run()
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        print(f"❌ 应用程序启动失败: {e}")
        print(f"详细错误信息: {type(e).__name__}: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        messagebox.showerror("启动错误", f"应用程序启动失败:\n{str(e)}")


if __name__ == "__main__":
    main() 