#!/usr/bin/env python3
"""
简单的配置编辑器
提供一个简单的GUI界面来编辑字段映射配置
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import os
from pathlib import Path


class SimpleConfigEditor:
    """简单配置编辑器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔧 字段映射配置编辑器")
        self.root.geometry("1000x700")
        
        # 配置文件路径
        self.excel_path = "data/config/字段映射配置.xlsx"
        
        # 当前编辑的数据
        self.current_data = {}
        self.current_marketplace = "fba"
        
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="字段映射配置编辑器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 商城选择
        marketplace_frame = ttk.Frame(main_frame)
        marketplace_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(marketplace_frame, text="选择商城:").grid(row=0, column=0, padx=(0, 10))
        
        self.marketplace_var = tk.StringVar(value="fba")
        marketplace_combo = ttk.Combobox(marketplace_frame, textvariable=self.marketplace_var, 
                                       values=["fba", "low_price"], state="readonly")
        marketplace_combo.grid(row=0, column=1, padx=(0, 20))
        marketplace_combo.bind('<<ComboboxSelected>>', self.on_marketplace_change)
        
        # 按钮框架
        button_frame = ttk.Frame(marketplace_frame)
        button_frame.grid(row=0, column=2, padx=(20, 0))
        
        ttk.Button(button_frame, text="📁 打开Excel文件", command=self.open_excel).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(button_frame, text="💾 保存配置", command=self.save_config).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(button_frame, text="🔄 重新加载", command=self.load_config).grid(row=0, column=2)
        
        # 表格框架
        table_frame = ttk.Frame(main_frame)
        table_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        # 创建表格
        columns = ("字段名", "列号", "字段显示名", "父体值", "子体值", "描述")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        
        # 设置列标题和宽度
        for col in columns:
            self.tree.heading(col, text=col)
            if col == "字段名":
                self.tree.column(col, width=150)
            elif col == "列号":
                self.tree.column(col, width=60)
            elif col == "字段显示名":
                self.tree.column(col, width=200)
            elif col in ["父体值", "子体值"]:
                self.tree.column(col, width=180)
            else:
                self.tree.column(col, width=300)
        
        # 滚动条
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局表格和滚动条
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 配置网格权重
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # 双击编辑
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def load_config(self):
        """加载配置文件"""
        try:
            if not os.path.exists(self.excel_path):
                self.status_var.set("Excel配置文件不存在，请先创建")
                return
            
            # 读取Excel文件
            excel_data = pd.read_excel(self.excel_path, sheet_name=None)
            self.current_data = excel_data
            
            # 更新显示
            self.update_display()
            self.status_var.set(f"已加载配置文件: {self.excel_path}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {e}")
            self.status_var.set("加载失败")
    
    def update_display(self):
        """更新表格显示"""
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 确定工作表名称
        sheet_name = "FBA商城字段映射" if self.current_marketplace == "fba" else "低价商城字段映射"
        
        if sheet_name not in self.current_data:
            self.status_var.set(f"未找到工作表: {sheet_name}")
            return
        
        # 获取数据
        df = self.current_data[sheet_name]
        
        # 添加数据到表格
        for _, row in df.iterrows():
            values = (
                str(row.get('字段名', '')),
                str(row.get('列号', '')),
                str(row.get('字段显示名', '')),
                str(row.get('父体值', '')),
                str(row.get('子体值', '')),
                str(row.get('描述', ''))
            )
            # 处理NaN值
            values = tuple('空' if v == 'nan' else v for v in values)
            self.tree.insert("", tk.END, values=values)
    
    def on_marketplace_change(self, event):
        """商城选择改变时的处理"""
        self.current_marketplace = self.marketplace_var.get()
        self.update_display()
    
    def on_item_double_click(self, event):
        """双击表格项时的处理"""
        item = self.tree.selection()[0]
        values = self.tree.item(item, "values")
        
        # 创建编辑对话框
        self.edit_field_dialog(item, values)
    
    def edit_field_dialog(self, item, values):
        """字段编辑对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑字段配置")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建输入框
        fields = ["字段名", "列号", "字段显示名", "父体值", "子体值", "描述"]
        entries = {}
        
        for i, field in enumerate(fields):
            ttk.Label(dialog, text=f"{field}:").grid(row=i, column=0, sticky=tk.W, padx=10, pady=5)
            
            if field == "描述":
                # 描述字段使用文本框
                text_widget = tk.Text(dialog, height=4, width=40)
                text_widget.grid(row=i, column=1, padx=10, pady=5, sticky=(tk.W, tk.E))
                text_widget.insert("1.0", values[i] if values[i] != '空' else '')
                entries[field] = text_widget
            else:
                # 其他字段使用输入框
                entry = ttk.Entry(dialog, width=40)
                entry.grid(row=i, column=1, padx=10, pady=5, sticky=(tk.W, tk.E))
                entry.insert(0, values[i] if values[i] != '空' else '')
                entries[field] = entry
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=len(fields), column=0, columnspan=2, pady=20)
        
        def save_changes():
            # 获取新值
            new_values = []
            for field in fields:
                if field == "描述":
                    value = entries[field].get("1.0", tk.END).strip()
                else:
                    value = entries[field].get().strip()
                new_values.append(value if value else '空')
            
            # 更新表格
            self.tree.item(item, values=new_values)
            dialog.destroy()
            self.status_var.set("字段已修改，请保存配置")
        
        def cancel_changes():
            dialog.destroy()
        
        ttk.Button(button_frame, text="保存", command=save_changes).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="取消", command=cancel_changes).grid(row=0, column=1, padx=5)
    
    def save_config(self):
        """保存配置到Excel文件"""
        try:
            # 获取当前表格数据
            sheet_name = "FBA商城字段映射" if self.current_marketplace == "fba" else "低价商城字段映射"
            
            data = []
            for item in self.tree.get_children():
                values = self.tree.item(item, "values")
                # 将'空'转换回空字符串
                row_data = {
                    '字段名': values[0] if values[0] != '空' else '',
                    '列号': values[1] if values[1] != '空' else '',
                    '字段显示名': values[2] if values[2] != '空' else '',
                    '父体值': values[3] if values[3] != '空' else '',
                    '子体值': values[4] if values[4] != '空' else '',
                    '描述': values[5] if values[5] != '空' else ''
                }
                data.append(row_data)
            
            # 更新数据
            self.current_data[sheet_name] = pd.DataFrame(data)
            
            # 保存到Excel文件
            with pd.ExcelWriter(self.excel_path, engine='openpyxl') as writer:
                for sheet, df in self.current_data.items():
                    df.to_excel(writer, sheet_name=sheet, index=False)
            
            messagebox.showinfo("成功", "配置已保存到Excel文件")
            self.status_var.set("配置已保存")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
            self.status_var.set("保存失败")
    
    def open_excel(self):
        """打开Excel文件"""
        try:
            import subprocess
            subprocess.run(['start', 'excel', self.excel_path], shell=True, check=True)
            self.status_var.set("已打开Excel文件")
        except Exception as e:
            messagebox.showwarning("提示", f"无法打开Excel文件: {e}\n请手动打开: {self.excel_path}")
    
    def run(self):
        """运行编辑器"""
        self.root.mainloop()


def main():
    """主函数"""
    # 确保配置文件存在
    excel_path = "data/config/字段映射配置.xlsx"
    if not os.path.exists(excel_path):
        print("Excel配置文件不存在，正在创建...")
        import sys
        sys.path.append('scripts')
        from create_excel_config import create_excel_config
        if not create_excel_config():
            print("创建Excel配置文件失败")
            return
    
    # 启动编辑器
    editor = SimpleConfigEditor()
    editor.run()


if __name__ == "__main__":
    main()
