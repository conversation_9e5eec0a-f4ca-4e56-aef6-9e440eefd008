"""
FBA商城实现
继承自BaseMarketplace，实现FBA商城特有的业务逻辑
"""

from typing import Dict, List, Any
from .base import BaseMarketplace, MarketplaceConfig
from .common_defaults import CommonDefaults, ValidationUtils, SKUGenerator
try:
    from ..config.field_mapping_loader import get_field_mapping_loader
except ImportError:
    # 处理相对导入问题
    import sys
    import os
    current_dir = os.path.dirname(__file__)
    parent_dir = os.path.dirname(current_dir)
    sys.path.insert(0, parent_dir)
    from config.field_mapping_loader import get_field_mapping_loader


class FBAMarketplace(BaseMarketplace):
    """FBA商城实现类"""
    
    def __init__(self):
        """初始化FBA商城配置"""
        config = MarketplaceConfig(
            name="fba",
            display_name="📦 FBA商城",
            template_file="data/input/DECORATIVE_RIBBON_TRIM_US.xlsm",  # 使用实际FBA模板
            product_file="data/input/产品资料库-丝带-20250416.xlsx",  # 暂时使用现有产品资料
            target_file="data/input/已上架低价商城listing.xlsx",  # 暂时使用现有目标文件
            supported_dimensions=["SKC", "MATERIAL_WIDTH", "MATERIAL_LENGTH"],  # FBA支持更多维度
            default_dimension="SKC",
            update_modes=["partial", "full"],
            default_update_mode="partial"
        )
        super().__init__(config)
    
    def get_field_mapping(self, update_mode: str = 'partial') -> Dict[str, Any]:
        """
        获取FBA商城字段映射关系
        优先从配置文件加载，失败时使用默认配置
        """
        try:
            # 从配置文件加载字段映射
            loader = get_field_mapping_loader()
            config_mode = 'partial_update' if update_mode == 'partial' else 'full_update'
            field_mappings = loader.get_field_mappings('fba', config_mode)

            if field_mappings:
                # 字段名映射表：配置字段名 -> 模板字段名
                field_name_mapping = {
                    'item_sku': 'Seller SKU',
                    'brand_name': 'Brand',
                    'update_delete': 'Update Delete',
                    'feed_product_type': 'Product Type',
                    'external_product_id': 'Product ID',
                    'external_product_id_type': 'Product ID Type',
                    'item_name': 'Title',
                    'manufacturer': 'Manufacturer',
                    'part_number': 'Manufacturer Part Number',
                    'product_category': 'Product Category',
                    'product_description': 'Product Description',
                    'model_name': 'Model Name',
                    'model_number': 'Model Number',
                    'standard_price': 'Your Price',
                    'quantity': 'Quantity',
                    'main_image_url': 'Main Image URL',
                    'parent_child': 'Parentage',
                    'parent_sku': 'Parent SKU',
                    'relationship_type': 'Relationship Type',
                    'variation_theme': 'Variation Theme',
                    'color_name': 'Color',
                    'size_name': 'Size',
                    'country_of_origin': 'Country/Region of Origin',
                    'batteries_required': 'Is this product a battery or does it utilize batteries?'
                }

                # 转换配置格式为代码期望的格式，并映射字段名
                converted_mappings = {}
                for config_field_name, field_config in field_mappings.items():
                    # 获取模板中的实际字段名
                    template_field_name = field_name_mapping.get(config_field_name, config_field_name)

                    if isinstance(field_config, dict):
                        if 'parent' in field_config and 'child' in field_config:
                            # 父子体不同值的字段
                            converted_mappings[template_field_name] = {
                                'parent': field_config['parent'],
                                'child': field_config['child']
                            }
                        elif 'source' in field_config:
                            # 直接映射的字段
                            converted_mappings[template_field_name] = field_config['source']
                        elif 'value' in field_config:
                            # 固定值字段
                            converted_mappings[template_field_name] = field_config['value']

                return converted_mappings
        except Exception as e:
            from loguru import logger
            logger.warning(f"从配置文件加载FBA字段映射失败，使用默认配置: {e}")

        # 如果配置文件加载失败，使用默认映射
        return self._get_default_field_mapping(update_mode)

    def _get_default_field_mapping(self, update_mode: str) -> Dict[str, Any]:
        """获取默认字段映射关系（配置文件加载失败时的备用方案）"""
        # FBA模板字段映射（基于分析的实际字段）
        base_mapping = {
            # 核心字段映射
            'item_sku': {  # 列2: Seller SKU
                'parent': 'generated',  # 父体SKU动态生成
                'child': 'target_listing.MSKU'  # 子体SKU直接映射
            },
            'brand_name': 'GENERIC',  # 列3: Brand - 固定值
            'update_delete': {  # 列4: Update Delete
                'parent': 'Update',
                'child': 'PartialUpdate'
            },
            'item_name': 'generated',  # 列7: Title - 动态生成
            'product_description': 'target_listing.品名',  # 列10: Product Description - 使用品名字段
            'standard_price': 'target_listing.价格',  # 列14: Your Price
            'quantity': 'target_listing.quantity',  # 列15: Quantity

            # 变体相关字段
            'parent_child': {  # 列26: Parentage
                'parent': 'Parent',
                'child': 'Child'
            },
            'parent_sku': 'generated_parent_sku',  # 列27: Parent SKU
            'relationship_type': 'Variation',  # 列28: Relationship Type
            'variation_theme': 'COLOR',  # 列29: Variation Theme

            # 颜色和尺寸
            'color_name': 'target_listing.color',  # 列39: Color
            'size_name': 'target_listing.size',  # 列44: Size

            # 其他重要字段
            'country_of_origin': 'China',  # 列134: Country/Region of Origin
            'batteries_required': 'No',  # 列107: Is this product a battery or does it utilize batteries?
        }

        if update_mode == 'full':
            # 完全更新模式的额外映射
            base_mapping.update({
                'manufacturer': 'GENERIC',  # 列8: Manufacturer
                'main_image_url': 'target_listing.image_url',  # 列16: Main Image URL
            })

        return base_mapping
    
    def get_default_values(self, record_type: str, update_mode: str = 'partial') -> Dict[str, Any]:
        """获取FBA商城默认值配置"""
        if record_type == 'parent':
            return self._get_parent_defaults(update_mode)
        elif record_type == 'child':
            return self._get_child_defaults(update_mode)
        else:
            raise ValueError(f"不支持的记录类型: {record_type}")
    
    def _get_parent_defaults(self, update_mode: str) -> Dict[str, Any]:
        """
        获取FBA父体默认值
        优先从配置文件加载，失败时使用代码中的默认值
        """
        try:
            # 从配置文件加载默认值
            loader = get_field_mapping_loader()
            config_defaults = loader.get_default_values('fba', 'parent')

            if config_defaults:
                # 如果是完全更新模式，添加额外字段
                if update_mode == 'full':
                    config_defaults.update({
                        'manufacturer': 'GENERIC',  # 列8: Manufacturer
                        'color_name': 'generated',  # 列39: Color - 将动态生成
                    })
                return config_defaults
        except Exception as e:
            from loguru import logger
            logger.warning(f"从配置文件加载FBA父体默认值失败，使用代码默认值: {e}")

        # 配置文件加载失败时的备用方案
        defaults = {
            # 基础字段默认值
            'feed_product_type': 'decorativeribbontrim',  # 列1: Product Type
            'brand_name': 'GENERIC',  # 列3: Brand
            'update_delete': 'Update',  # 列4: Update Delete - 父体使用Update
            'item_name': 'generated',  # 列7: Title - 将动态生成
            'product_description': 'generated',  # 列10: Product Description - 将动态生成

            # 变体相关默认值
            'parent_child': 'Parent',  # 列26: Parentage
            'relationship_type': 'Variation',  # 列28: Relationship Type
            'variation_theme': 'COLOR',  # 列29: Variation Theme

            # 其他重要默认值
            'country_of_origin': 'China',  # 列134: Country/Region of Origin
            'batteries_required': 'No',  # 列107: Is this product a battery or does it utilize batteries?
        }

        if update_mode == 'full':
            # 完全更新模式的额外默认值
            defaults.update({
                'manufacturer': 'GENERIC',  # 列8: Manufacturer
                'color_name': 'generated',  # 列39: Color - 将动态生成
            })

        return defaults
    
    def _get_child_defaults(self, update_mode: str) -> Dict[str, Any]:
        """
        获取FBA子体默认值
        优先从配置文件加载，失败时使用代码中的默认值
        """
        try:
            # 从配置文件加载默认值
            loader = get_field_mapping_loader()
            config_defaults = loader.get_default_values('fba', 'child')

            if config_defaults:
                # 如果是完全更新模式，添加额外字段
                if update_mode == 'full':
                    config_defaults.update({
                        'manufacturer': 'GENERIC',  # 列8: Manufacturer
                        'item_name': 'generated',  # 列7: Title - 将动态生成
                        'product_description': 'generated',  # 列10: Product Description - 将动态生成
                    })
                return config_defaults
        except Exception as e:
            from loguru import logger
            logger.warning(f"从配置文件加载FBA子体默认值失败，使用代码默认值: {e}")

        # 配置文件加载失败时的备用方案
        defaults = {
            # 基础字段默认值
            'feed_product_type': 'decorativeribbontrim',  # 列1: Product Type
            'brand_name': 'GENERIC',  # 列3: Brand
            'update_delete': 'PartialUpdate',  # 列4: Update Delete - 子体使用PartialUpdate

            # 变体相关默认值
            'parent_child': 'Child',  # 列26: Parentage
            'parent_sku': 'generated_parent_sku',  # 列27: Parent SKU - 引用父体SKU
            'relationship_type': 'Variation',  # 列28: Relationship Type
            'variation_theme': 'COLOR',  # 列29: Variation Theme

            # 颜色和尺寸（从数据映射）
            'color_name': 'from_data',  # 列39: Color - 从目标数据获取
            'size_name': 'from_data',  # 列44: Size - 从目标数据获取

            # 其他重要默认值
            'country_of_origin': 'China',  # 列134: Country/Region of Origin
            'batteries_required': 'No',  # 列107: Is this product a battery or does it utilize batteries?
        }

        if update_mode == 'full':
            # 完全更新模式的额外默认值
            defaults.update({
                'manufacturer': 'GENERIC',  # 列8: Manufacturer
                'item_name': 'generated',  # 列7: Title - 将动态生成
                'product_description': 'generated',  # 列10: Product Description - 将动态生成
            })

        return defaults
    
    def generate_parent_sku(self, group_data: Dict[str, Any], dimension: str) -> str:
        """
        生成FBA商城父体SKU

        Args:
            group_data: 分组数据
            dimension: 父体维度

        Returns:
            生成的父体SKU
        """
        if dimension == "SKC":
            # 使用通用SKU生成器，FBA商城添加前缀，不添加PARENT后缀
            return SKUGenerator.generate_basic_parent_sku(group_data, dimension, prefix="FBA-")

        elif dimension == "MATERIAL_WIDTH":
            # 材质+宽度维度：基于材质和宽度生成
            material = group_data.get('material', 'UNKNOWN')
            width = group_data.get('width', 'UNKNOWN')
            return f"FBA-{material}-{width}"

        elif dimension == "MATERIAL_LENGTH":
            # 材质+长度维度：基于材质和长度生成
            material = group_data.get('material', 'UNKNOWN')
            length = group_data.get('length', 'UNKNOWN')
            return f"FBA-{material}-{length}"

        else:
            raise ValueError(f"不支持的父体维度: {dimension}")
    
    def generate_item_name(self, size: str, keyword: str) -> str:
        """
        生成FBA商城商品名称

        Args:
            size: 尺寸信息
            keyword: 商品关键词

        Returns:
            商品名称
        """
        # 使用通用商品名称生成器，FBA商城添加前缀
        return SKUGenerator.generate_basic_item_name(size, keyword, prefix="FBA")
    
    def validate_data(self, data: Dict[str, Any]) -> tuple[bool, str]:
        """
        验证FBA商城数据

        Args:
            data: 待验证的数据

        Returns:
            (是否验证通过, 错误信息)
        """
        # FBA商城的验证规则可能更严格
        required_fields = ['MSKU', 'color']  # 暂时移除FC_ID要求，因为现有数据中没有

        # 使用通用验证工具
        is_valid, message = ValidationUtils.validate_required_fields(data, required_fields)
        if not is_valid:
            return False, f"FBA{message}"

        # 检查MSKU格式
        msku = data.get('MSKU', '')
        is_valid, message = ValidationUtils.validate_msku_format(msku)
        if not is_valid:
            return False, f"FBA{message}"

        return True, "FBA数据验证通过"
    
    def get_template_start_row(self) -> Dict[str, int]:
        """
        获取FBA模板填充起始行
        基于DECORATIVE_RIBBON_TRIM_US.xlsm模板的实际结构

        Returns:
            包含parent和child起始行的字典
        """
        return {
            'parent': 5,  # FBA模板数据从第5行开始（第4行是示例）
            'child': 6    # 子体从第6行开始
        }

    def get_template_header_row(self) -> int:
        """
        获取FBA模板表头行
        基于DECORATIVE_RIBBON_TRIM_US.xlsm模板的实际结构

        Returns:
            表头行号（1基索引）
        """
        return 2  # FBA模板第2行是字段显示名称（0基索引）

    def get_field_code_row(self) -> int:
        """
        获取FBA模板字段代码行

        Returns:
            字段代码行号（1基索引）
        """
        return 3  # FBA模板第3行是字段代码名称（API字段名）
