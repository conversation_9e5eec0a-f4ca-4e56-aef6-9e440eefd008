"""
简化版GUI窗口
用于测试多商城架构的基本功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# 设置Python路径
current_dir = os.path.dirname(__file__)
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 导入模块
from marketplace.factory import MarketplaceFactory
from config.settings import Settings


class SimpleWindow:
    """简化版主窗口"""
    
    def __init__(self):
        """初始化窗口"""
        self.root = tk.Tk()
        self.settings = Settings()
        
        # 商城相关变量
        self.marketplace_var = tk.StringVar()
        self.available_marketplaces = MarketplaceFactory.get_available_marketplaces()
        self.current_marketplace = None
        
        # 其他变量
        self.keyword_var = tk.StringVar()
        
        # 初始化默认商城
        self.initialize_marketplace()
        
        self.setup_ui()
    
    def initialize_marketplace(self):
        """初始化商城设置"""
        # 使用默认商城
        default_marketplace = self.settings.DEFAULT_MARKETPLACE
        if default_marketplace in self.available_marketplaces:
            self.marketplace_var.set(self.available_marketplaces[default_marketplace])
            self.current_marketplace = MarketplaceFactory.create_marketplace(default_marketplace)
        else:
            # 使用第一个可用商城
            first_marketplace = list(self.available_marketplaces.keys())[0]
            self.marketplace_var.set(self.available_marketplaces[first_marketplace])
            self.current_marketplace = MarketplaceFactory.create_marketplace(first_marketplace)
    
    def setup_ui(self):
        """设置用户界面"""
        self.root.title(f"{self.settings.APP_NAME} - 简化版")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text=f"🏪 {self.settings.APP_NAME}", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 商城选择
        self.create_marketplace_section(main_frame)
        
        # 关键词输入
        self.create_keyword_section(main_frame)
        
        # 信息显示
        self.create_info_section(main_frame)
        
        # 按钮
        self.create_buttons(main_frame)
        
        # 状态显示
        self.create_status_section(main_frame)
    
    def create_marketplace_section(self, parent):
        """创建商城选择区域"""
        frame = ttk.LabelFrame(parent, text="🏪 商城类型选择", padding="10")
        frame.pack(fill=tk.X, pady=(0, 10))
        
        # 商城选择
        selection_frame = ttk.Frame(frame)
        selection_frame.pack(fill=tk.X)
        
        ttk.Label(selection_frame, text="选择商城:", 
                 font=("Microsoft YaHei", 10)).pack(side=tk.LEFT, padx=(0, 10))
        
        marketplace_options = list(self.available_marketplaces.values())
        self.marketplace_combo = ttk.Combobox(selection_frame,
                                            textvariable=self.marketplace_var,
                                            values=marketplace_options,
                                            state="readonly",
                                            font=("Microsoft YaHei", 10),
                                            width=30)
        self.marketplace_combo.pack(side=tk.LEFT)
        self.marketplace_combo.bind('<<ComboboxSelected>>', self.on_marketplace_changed)
    
    def create_keyword_section(self, parent):
        """创建关键词输入区域"""
        frame = ttk.LabelFrame(parent, text="🔍 商品关键词", padding="10")
        frame.pack(fill=tk.X, pady=(0, 10))
        
        input_frame = ttk.Frame(frame)
        input_frame.pack(fill=tk.X)
        
        ttk.Label(input_frame, text="关键词:", 
                 font=("Microsoft YaHei", 10)).pack(side=tk.LEFT, padx=(0, 10))
        
        keyword_entry = ttk.Entry(input_frame, textvariable=self.keyword_var, 
                                 font=("Microsoft YaHei", 10))
        keyword_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        ttk.Label(input_frame, text="示例: satin ribbon", 
                 foreground="gray", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)
    
    def create_info_section(self, parent):
        """创建信息显示区域"""
        frame = ttk.LabelFrame(parent, text="📋 当前配置", padding="10")
        frame.pack(fill=tk.X, pady=(0, 10))
        
        self.info_text = tk.Text(frame, height=8, wrap=tk.WORD, 
                                font=("Consolas", 9),
                                bg="#f8f9fa", fg="#333333",
                                relief=tk.FLAT, bd=1,
                                state=tk.DISABLED)
        self.info_text.pack(fill=tk.BOTH, expand=True)
        
        # 更新信息显示
        self.update_info_display()
    
    def create_buttons(self, parent):
        """创建按钮区域"""
        frame = ttk.Frame(parent)
        frame.pack(pady=10)
        
        # 测试按钮
        test_btn = ttk.Button(frame, text="🧪 测试商城配置", 
                             command=self.test_marketplace)
        test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        refresh_btn = ttk.Button(frame, text="🔄 刷新信息", 
                               command=self.update_info_display)
        refresh_btn.pack(side=tk.LEFT)
    
    def create_status_section(self, parent):
        """创建状态显示区域"""
        frame = ttk.LabelFrame(parent, text="📝 操作日志", padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        self.status_text = tk.Text(frame, height=6, wrap=tk.WORD, 
                                  font=("Consolas", 9),
                                  bg="#f8f9fa", fg="#333333",
                                  relief=tk.FLAT, bd=1,
                                  state=tk.DISABLED)
        self.status_text.pack(fill=tk.BOTH, expand=True)
        
        # 显示欢迎信息
        self.log_message("🎉 简化版GUI启动成功！")
        self.log_message("💡 这是用于测试多商城架构的简化版界面")
    
    def on_marketplace_changed(self, event=None):
        """商城类型改变处理"""
        selected_display_name = self.marketplace_var.get()
        
        # 找到对应的商城类型代码
        marketplace_type = None
        for code, display_name in self.available_marketplaces.items():
            if display_name == selected_display_name:
                marketplace_type = code
                break
        
        if marketplace_type:
            self.current_marketplace = MarketplaceFactory.create_marketplace(marketplace_type)
            self.update_info_display()
            self.log_message(f"🏪 已切换到: {selected_display_name}")
    
    def update_info_display(self):
        """更新信息显示"""
        if not self.current_marketplace:
            return
        
        config = self.current_marketplace.config
        
        info_text = f"""商城信息:
• 名称: {config.display_name}
• 类型代码: {config.name}
• 模板文件: {os.path.basename(config.template_file)}
• 产品资料: {os.path.basename(config.product_file)}
• 目标文件: {os.path.basename(config.target_file)}
• 支持维度: {', '.join(config.supported_dimensions)}
• 更新模式: {', '.join(config.update_modes)}
• 默认维度: {config.default_dimension}
• 默认更新模式: {config.default_update_mode}

测试数据:
• 父体SKU示例: {self.get_sample_parent_sku()}
• 商品名称示例: {self.get_sample_item_name()}
"""
        
        self.info_text.configure(state=tk.NORMAL)
        self.info_text.delete('1.0', tk.END)
        self.info_text.insert('1.0', info_text)
        self.info_text.configure(state=tk.DISABLED)
    
    def get_sample_parent_sku(self):
        """获取示例父体SKU"""
        try:
            group_data = {'SKC': 'SAMPLE-SKC-001', 'children': []}
            return self.current_marketplace.generate_parent_sku(group_data, 'SKC')
        except:
            return "生成失败"
    
    def get_sample_item_name(self):
        """获取示例商品名称"""
        try:
            keyword = self.keyword_var.get() or "sample product"
            return self.current_marketplace.generate_item_name('3/8"-100 Yards', keyword)
        except:
            return "生成失败"
    
    def test_marketplace(self):
        """测试商城配置"""
        if not self.current_marketplace:
            messagebox.showerror("错误", "没有选择商城类型")
            return
        
        try:
            # 测试基本方法
            config = self.current_marketplace.config
            parent_defaults = self.current_marketplace.get_default_values('parent', 'partial')
            child_defaults = self.current_marketplace.get_default_values('child', 'partial')
            
            # 测试数据验证
            test_data = {'MSKU': 'TEST-001', 'color': 'Red'}
            is_valid, message = self.current_marketplace.validate_data(test_data)
            
            result = f"""测试结果:
✅ 商城配置: {config.display_name}
✅ 父体默认值: {len(parent_defaults)}个字段
✅ 子体默认值: {len(child_defaults)}个字段
✅ 数据验证: {'通过' if is_valid else '失败'} - {message}
✅ 父体SKU生成: {self.get_sample_parent_sku()}
✅ 商品名称生成: {self.get_sample_item_name()}
"""
            
            messagebox.showinfo("测试结果", result)
            self.log_message("🧪 商城配置测试完成")
            
        except Exception as e:
            messagebox.showerror("测试失败", f"测试过程中出现错误:\n{str(e)}")
            self.log_message(f"❌ 测试失败: {str(e)}")
    
    def log_message(self, message):
        """记录日志消息"""
        self.status_text.configure(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.configure(state=tk.DISABLED)
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SimpleWindow()
        app.run()
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
