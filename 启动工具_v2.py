"""
亚马逊Listing创建工具 v2.0 - 启动脚本
解决导入路径问题的启动脚本
"""

import os
import sys

def setup_python_path():
    """设置Python路径"""
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 添加src目录到Python路径
    src_dir = os.path.join(script_dir, 'src')
    if src_dir not in sys.path:
        sys.path.insert(0, src_dir)
    
    # 添加项目根目录到Python路径
    if script_dir not in sys.path:
        sys.path.insert(0, script_dir)
    
    print(f"📁 项目根目录: {script_dir}")
    print(f"📁 源码目录: {src_dir}")

def check_dependencies():
    """检查依赖库"""
    required_packages = [
        'pandas',
        'openpyxl', 
        'loguru',
        'tkinter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动亚马逊listing拆分合并工具 v2.0...")
    print("=" * 50)
    
    # 设置Python路径
    setup_python_path()
    
    # 检查依赖
    print("\n🔍 检查依赖库...")
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    print("\n✅ 依赖检查通过")
    
    try:
        # 导入并启动应用
        print("\n🏪 启动应用程序...")
        
        # 设置日志
        from config.settings import Settings
        Settings.ensure_directories()
        
        from loguru import logger
        logger.add(
            Settings.get_log_file_path(),
            rotation=Settings.LOG_ROTATION,
            retention=Settings.LOG_RETENTION,
            level=Settings.LOG_LEVEL,
            format=Settings.LOG_FORMAT
        )
        
        logger.info(f"🏪 {Settings.APP_NAME}启动")

        # 启动GUI
        print("🔧 准备启动GUI界面...")

        # 确保标准输出不被缓冲，以便调试信息能正确显示
        import sys
        try:
            sys.stdout.reconfigure(line_buffering=True)
            sys.stderr.reconfigure(line_buffering=True)
        except AttributeError:
            # Python 3.6及以下版本不支持reconfigure
            pass

        from gui.main_window import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"\n❌ 导入模块失败: {e}")
        print(f"详细错误信息: {type(e).__name__}: {str(e)}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        print("请检查项目结构是否完整")
        input("按回车键退出...")
    except Exception as e:
        print(f"\n❌ 程序启动失败: {e}")
        print(f"详细错误信息: {type(e).__name__}: {str(e)}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
