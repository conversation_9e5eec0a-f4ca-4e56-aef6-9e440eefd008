# ⚙️ 项目配置说明

## 📋 快速配置指南

### 核心配置项
```python
# 默认配置
DEFAULT_CONFIG = {
    # 输入文件路径
    "target_listing_file": "data/input/已上架低价商城listing.xlsx",
    "product_data_file": "data/input/产品资料库-丝带-20250416.xlsx", 
    "amazon_template_file": "data/input/低价商城template.xlsm",
    
    # 输出路径
    "output_directory": "data/output/",
    "log_directory": "data/logs/",
    
    # 业务配置
    "update_mode": "partial",  # partial | full
    "parent_dimension": "skc", # skc维度分组
    
    # 系统配置
    "max_records": 1000,       # 单次处理最大记录数
    "log_level": "INFO"        # DEBUG | INFO | WARNING | ERROR
}
```

## 🔗 字段映射配置

### 基础映射关系
```python
FIELD_MAPPING = {
    # 直接映射
    "MSKU": "Seller SKU",  # 子体
    "Product Description": "Product Description",
    "color": "Color",
    
    # 组合映射
    "Item Name": "size + 商品关键词",  # 父体
    
    # SKC维度映射
    "Parent Seller SKU": "SKU -> 产品资料.SKC"  # 父体
}
```

### 默认值配置
```python
DEFAULT_VALUES = {
    # 父体默认值
    "parent": {
        "Record Action": "Full Update",
        "Product Type": "HOME",
        "Brand Name": "GENERIC",
        "Parentage Level": "Parent",
        "Child Relationship Type": "Variation",
        "Variation Theme Name": "COLOR",
        "Country of Origin": "China",
        "Are batteries required?": "No",
        "Dangerous Goods Regulations": "Not Applicable"
    },
    
    # 子体默认值
    "child": {
        "Record Action": "Partial Update",
        "Product Type": "HOME",
        "Brand Name": "GENERIC",
        "Parentage Level": "Child",
        "Child Relationship Type": "Variation",
        "Variation Theme Name": "COLOR",
        "Country of Origin": "China",
        "Are batteries required?": "No",
        "Dangerous Goods Regulations": "Not Applicable"
    }
}
```

## 📁 文件结构配置

### 输入文件规范
```python
INPUT_FILE_SPECS = {
    "target_listing": {
        "file_format": ".xlsx",
        "worksheet": "sheet1",
        "header_row": 1,
        "required_fields": ["MSKU", "SKU", "color", "size", "Product Description"]
    },
    
    "product_data": {
        "file_format": ".xlsx", 
        "worksheet": "单个产品",
        "header_row": 1,
        "required_fields": ["*SKU（必填）"]
    },
    
    "amazon_template": {
        "file_format": [".xlsm", ".xlsx"],
        "worksheet": "Template",
        "header_row": 4,
        "parent_start_row": 7,
        "child_start_row": 8,
        "total_columns": 288
    }
}
```

## 🎯 业务规则配置

### 更新模式配置
```python
UPDATE_MODES = {
    "partial": {
        "parent_fields": [
            "Seller SKU", "Record Action", "Product Type", "Item Name", 
            "Brand Name", "Item Type Keyword", "Product Description", 
            "Bullet Point", "Parentage Level", "Child Relationship Type",
            "Variation Theme Name", "Country of Origin", 
            "Are batteries required?", "Dangerous Goods Regulations"
        ],
        "child_fields": [
            "Seller SKU", "Record Action", "Product Type", "Brand Name", 
            "Color", "Parentage Level", "Child Relationship Type", 
            "Parent SKU", "Variation Theme Name", "Country of Origin",
            "Are batteries required?", "Dangerous Goods Regulations"
        ]
    },
    
    "full": {
        "parent_fields": [
            # 部分更新字段 + Color
            "Seller SKU", "Record Action", "Product Type", "Item Name",
            "Brand Name", "Item Type Keyword", "Product Description",
            "Bullet Point", "Color", "Parentage Level", "Child Relationship Type",
            "Variation Theme Name", "Country of Origin",
            "Are batteries required?", "Dangerous Goods Regulations"
        ],
        "child_fields": [
            # 部分更新字段 + Item Type Keyword, Product Description, Bullet Point
            "Seller SKU", "Record Action", "Product Type", "Brand Name",
            "Item Type Keyword", "Product Description", "Bullet Point",
            "Color", "Parentage Level", "Child Relationship Type",
            "Parent SKU", "Variation Theme Name", "Country of Origin",
            "Are batteries required?", "Dangerous Goods Regulations"
        ]
    }
}
```

### 父体维度配置
```python
PARENT_DIMENSIONS = {
    "skc": {
        "name": "SKC维度",
        "description": "SKC相同的Seller SKU为一个父体",
        "grouping_logic": "目标listing表格.SKU -> 产品资料.SKC",
        "enabled": True
    },
    
    "material_width": {
        "name": "材质+宽度维度", 
        "description": "同材质+同宽度+不同长度",
        "grouping_logic": "材质 + 宽度",
        "enabled": False  # 未来功能
    },
    
    "custom": {
        "name": "自定义维度",
        "description": "用户自定义分组规则",
        "grouping_logic": "用户定义",
        "enabled": False  # 未来功能
    }
}
```

## 🔧 系统配置

### 性能配置
```python
PERFORMANCE_CONFIG = {
    "batch_size": 100,          # 批处理大小
    "max_memory_usage": "1GB",  # 最大内存使用
    "thread_pool_size": 4,      # 线程池大小
    "cache_enabled": True,      # 启用缓存
    "cache_size": 1000         # 缓存大小
}
```

### 日志配置
```python
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_rotation": True,
    "max_file_size": "10MB",
    "backup_count": 5,
    "console_output": True
}
```

### 错误处理配置
```python
ERROR_HANDLING = {
    "continue_on_error": True,     # 遇到错误时继续处理
    "max_error_count": 10,         # 最大错误数量
    "error_report_enabled": True,  # 启用错误报告
    "auto_retry": True,            # 自动重试
    "retry_count": 3               # 重试次数
}
```

## 🎨 界面配置

### GUI配置
```python
GUI_CONFIG = {
    "window_title": "低价商城Listing创建工具",
    "window_size": "800x600",
    "theme": "default",
    "font_family": "Microsoft YaHei",
    "font_size": 10,
    "language": "zh_CN"
}
```

### 控件配置
```python
WIDGET_CONFIG = {
    "keyword_input": {
        "placeholder": "请输入商品关键词，如：satin ribbon",
        "max_length": 100
    },
    "file_selector": {
        "default_path": "data/input/",
        "file_types": [("Excel files", "*.xlsx *.xlsm")]
    },
    "progress_bar": {
        "show_percentage": True,
        "update_interval": 100  # ms
    }
}
```

## 📊 验证配置

### 数据验证规则
```python
VALIDATION_RULES = {
    "required_fields": {
        "target_listing": ["MSKU", "SKU", "color", "size"],
        "product_data": ["*SKU（必填）"],
        "amazon_template": ["Seller SKU"]
    },
    
    "data_types": {
        "MSKU": "string",
        "color": "string", 
        "size": "string",
        "SKU": "string"
    },
    
    "value_constraints": {
        "MSKU": {"min_length": 1, "max_length": 50},
        "color": {"min_length": 1, "max_length": 30},
        "size": {"min_length": 1, "max_length": 50}
    }
}
```

## 🔄 版本配置

### 版本信息
```python
VERSION_INFO = {
    "version": "1.0.0",
    "release_date": "2025-07-28",
    "python_version": "3.8+",
    "dependencies": {
        "pandas": ">=1.3.0",
        "openpyxl": ">=3.0.0", 
        "tkinter": "built-in"
    }
}
```

## 📝 使用说明

### 配置文件位置
- **主配置**: `src/config.py`
- **用户配置**: `data/config/user_config.json`（可选）
- **默认配置**: 代码中的DEFAULT_CONFIG

### 配置优先级
1. 用户自定义配置（最高优先级）
2. 环境变量配置
3. 默认配置（最低优先级）

### 配置修改方法
1. **GUI界面**: 通过界面设置修改常用配置
2. **配置文件**: 直接编辑配置文件
3. **命令行参数**: 启动时传入参数覆盖配置

## ⚠️ 注意事项

1. **配置兼容性**: 修改配置前请备份原配置文件
2. **路径格式**: Windows系统使用反斜杠或双反斜杠
3. **编码格式**: 配置文件使用UTF-8编码
4. **权限要求**: 确保对配置文件有读写权限
