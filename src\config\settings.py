"""
全局设置配置
定义应用程序的全局配置项
"""

import os
from typing import Dict, Any
from dataclasses import dataclass, field


@dataclass
class Settings:
    """全局设置类"""
    
    # 项目根目录
    PROJECT_ROOT: str = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 数据目录
    DATA_DIR: str = os.path.join(PROJECT_ROOT, "data")
    INPUT_DIR: str = os.path.join(DATA_DIR, "input")
    OUTPUT_DIR: str = os.path.join(DATA_DIR, "output")
    LOGS_DIR: str = os.path.join(DATA_DIR, "logs")
    
    # 应用程序信息
    APP_NAME: str = "亚马逊listing拆分合并工具"
    APP_VERSION: str = "v2.0.0"

    # GUI配置
    WINDOW_TITLE: str = f"🛠️ {APP_NAME} {APP_VERSION}"
    WINDOW_SIZE: str = "800x550"  # 大幅减少窗口大小（减少约1/3）
    MIN_WINDOW_SIZE: tuple = (750, 500)  # 调整最小窗口大小
    
    # 日志配置
    LOG_FILE: str = os.path.join(LOGS_DIR, "listing_tool.log")
    LOG_ROTATION: str = "10 MB"
    LOG_RETENTION: str = "7 days"
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    
    # 默认配置
    DEFAULT_MARKETPLACE: str = "low_price"
    DEFAULT_UPDATE_MODE: str = "partial"
    DEFAULT_DIMENSION: str = "SKC"
    
    # 文件配置
    SUPPORTED_FILE_TYPES: Dict[str, str] = field(default_factory=lambda: {
        "Excel文件": "*.xlsx",
        "Excel宏文件": "*.xlsm",
        "所有Excel文件": "*.xlsx;*.xlsm"
    })
    
    # 性能配置
    MAX_RECORDS_PER_BATCH: int = 1000
    THREAD_TIMEOUT: int = 300  # 5分钟
    
    # UI样式配置
    UI_THEME: str = "vista"
    UI_FONT_FAMILY: str = "Microsoft YaHei"
    UI_FONT_SIZE_TITLE: int = 18
    UI_FONT_SIZE_SECTION: int = 12
    UI_FONT_SIZE_NORMAL: int = 10
    UI_FONT_SIZE_SMALL: int = 9
    
    # 颜色配置
    COLOR_PRIMARY: str = "#2E86AB"
    COLOR_SUCCESS: str = "#28a745"
    COLOR_WARNING: str = "#ffc107"
    COLOR_ERROR: str = "#dc3545"
    COLOR_INFO: str = "#17a2b8"
    COLOR_SECONDARY: str = "#6c757d"
    COLOR_BACKGROUND: str = "#f8f9fa"
    COLOR_TEXT: str = "#333333"
    COLOR_TEXT_MUTED: str = "#6c757d"
    
    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        directories = [
            cls.DATA_DIR,
            cls.INPUT_DIR,
            cls.OUTPUT_DIR,
            cls.LOGS_DIR
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def get_input_file_path(cls, filename: str) -> str:
        """
        获取输入文件的完整路径
        
        Args:
            filename: 文件名
            
        Returns:
            完整文件路径
        """
        return os.path.join(cls.INPUT_DIR, filename)
    
    @classmethod
    def get_output_file_path(cls, filename: str) -> str:
        """
        获取输出文件的完整路径
        
        Args:
            filename: 文件名
            
        Returns:
            完整文件路径
        """
        return os.path.join(cls.OUTPUT_DIR, filename)
    
    @classmethod
    def get_log_file_path(cls) -> str:
        """
        获取日志文件路径
        
        Returns:
            日志文件路径
        """
        return cls.LOG_FILE
