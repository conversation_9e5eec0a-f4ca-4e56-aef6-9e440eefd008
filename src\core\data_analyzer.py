"""
数据分析模块
用于分析输入文件的结构和数据格式
"""

import pandas as pd
import os
from typing import Dict, List, Tuple, Any
from loguru import logger


class DataAnalyzer:
    """数据分析器类"""
    
    def __init__(self):
        """初始化数据分析器"""
        self.base_path = os.path.join(os.path.dirname(__file__), '..', '..')
        
    def analyze_excel_file(self, file_path: str) -> Dict[str, Any]:
        """
        分析Excel文件结构
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            包含文件结构信息的字典
        """
        try:
            # 获取所有工作表名称
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            analysis_result = {
                'file_path': file_path,
                'sheet_names': sheet_names,
                'sheets_data': {}
            }
            
            # 分析每个工作表
            for sheet_name in sheet_names:
                try:
                    # 读取工作表数据（前10行用于分析）
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=10)
                    
                    sheet_info = {
                        'columns': df.columns.tolist(),
                        'shape': df.shape,
                        'sample_data': df.head(3).to_dict('records') if len(df) > 0 else []
                    }
                    
                    analysis_result['sheets_data'][sheet_name] = sheet_info
                    
                except Exception as e:
                    logger.warning(f"无法分析工作表 {sheet_name}: {e}")
                    analysis_result['sheets_data'][sheet_name] = {'error': str(e)}
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析文件 {file_path} 时出错: {e}")
            return {'error': str(e)}
    
    def analyze_target_listing(self) -> Dict[str, Any]:
        """
        分析目标listing表格
        
        Returns:
            目标listing表格的分析结果
        """
        file_path = os.path.join(self.base_path, 'data', 'input', '已上架低价商城listing.xlsx')
        logger.info(f"分析目标listing表格: {file_path}")
        return self.analyze_excel_file(file_path)
    
    def analyze_product_data(self) -> Dict[str, Any]:
        """
        分析产品资料表格
        
        Returns:
            产品资料表格的分析结果
        """
        file_path = os.path.join(self.base_path, 'data', 'input', '产品资料库-丝带-20250416.xlsx')
        logger.info(f"分析产品资料表格: {file_path}")
        return self.analyze_excel_file(file_path)
    
    def analyze_template(self) -> Dict[str, Any]:
        """
        分析亚马逊模板
        
        Returns:
            亚马逊模板的分析结果
        """
        file_path = os.path.join(self.base_path, 'data', 'input', '低价商城template.xlsm')
        logger.info(f"分析亚马逊模板: {file_path}")
        return self.analyze_excel_file(file_path)
    
    def print_analysis_summary(self, analysis_result: Dict[str, Any]) -> None:
        """
        打印分析结果摘要
        
        Args:
            analysis_result: 分析结果字典
        """
        if 'error' in analysis_result:
            print(f"❌ 分析出错: {analysis_result['error']}")
            return
        
        print(f"📁 文件: {os.path.basename(analysis_result['file_path'])}")
        print(f"📊 工作表数量: {len(analysis_result['sheet_names'])}")
        print(f"📋 工作表列表: {', '.join(analysis_result['sheet_names'])}")
        
        for sheet_name, sheet_data in analysis_result['sheets_data'].items():
            if 'error' in sheet_data:
                print(f"  ❌ {sheet_name}: {sheet_data['error']}")
            else:
                print(f"  📄 {sheet_name}: {sheet_data['shape'][1]}列 x {sheet_data['shape'][0]}行")
                if sheet_data['columns']:
                    print(f"     字段: {', '.join(sheet_data['columns'][:5])}{'...' if len(sheet_data['columns']) > 5 else ''}")


if __name__ == "__main__":
    # 测试数据分析功能
    analyzer = DataAnalyzer()
    
    print("🔍 开始分析项目文件...")
    print("=" * 50)
    
    # 分析目标listing
    print("1️⃣ 分析目标listing表格")
    target_analysis = analyzer.analyze_target_listing()
    analyzer.print_analysis_summary(target_analysis)
    print()
    
    # 分析产品资料
    print("2️⃣ 分析产品资料表格")
    product_analysis = analyzer.analyze_product_data()
    analyzer.print_analysis_summary(product_analysis)
    print()
    
    # 分析模板
    print("3️⃣ 分析亚马逊模板")
    template_analysis = analyzer.analyze_template()
    analyzer.print_analysis_summary(template_analysis) 