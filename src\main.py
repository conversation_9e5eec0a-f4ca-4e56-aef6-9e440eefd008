"""
亚马逊Listing创建工具 - 主程序入口
支持多种商城类型（低价商城、FBA商城等）
"""

import os
import sys
from loguru import logger

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 获取项目根目录
sys.path.insert(0, current_dir)  # 添加src目录
sys.path.insert(0, project_root)  # 添加项目根目录

def setup_logging():
    """设置日志配置"""
    from config.settings import Settings

    # 确保目录存在
    Settings.ensure_directories()

    # 配置日志
    logger.add(
        Settings.get_log_file_path(),
        rotation=Settings.LOG_ROTATION,
        retention=Settings.LOG_RETENTION,
        level=Settings.LOG_LEVEL,
        format=Settings.LOG_FORMAT
    )

def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()

        from config.settings import Settings
        logger.info(f"🏪 {Settings.APP_NAME}启动")

        # 导入并启动GUI
        from gui.main_window import main as gui_main
        gui_main()

    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print(f"详细错误信息: {type(e).__name__}: {str(e)}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        print("请确保已安装所有依赖库: pip install -r requirements.txt")
        input("按回车键退出...")
    except Exception as e:
        try:
            logger.error(f"程序启动失败: {e}")
            logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
        except:
            pass  # 如果logger还没初始化，忽略日志记录错误
        print(f"❌ 程序启动失败: {e}")
        print(f"详细错误信息: {type(e).__name__}: {str(e)}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        input("按回车键退出...")

if __name__ == "__main__":
    main() 