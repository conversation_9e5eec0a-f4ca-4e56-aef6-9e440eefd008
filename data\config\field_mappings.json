{"description": "亚马逊listing拆分合并工具 - 字段映射配置文件", "version": "1.0.0", "last_updated": "2025-07-28", "marketplaces": {"low_price": {"name": "低价商城", "description": "低价商城字段映射配置", "field_mappings": {"partial_update": {"seller_sku": {"parent": "generated", "child": "target_listing.MSKU", "description": "卖家SKU - 父体动态生成，子体直接映射MSKU"}, "record_action": {"parent": "Full Update", "child": "Partial Update", "description": "记录操作 - 父体为Full Update，子体为Partial Update"}, "product_description": {"source": "target_listing.品名", "description": "产品描述 - 使用品名字段"}, "color": {"source": "target_listing.color", "description": "颜色 - 直接映射"}, "size": {"source": "target_listing.size", "description": "尺寸 - 直接映射"}}, "full_update": {"additional_field": {"source": "some_mapping", "description": "完全更新模式的额外字段"}}}, "default_values": {"parent": {"record_action": "Full Update", "Product Type": "HOME", "Brand Name": "GENERIC", "Parentage Level": "Parent", "Child Relationship Type": "Variation", "Variation Theme Name": "COLOR", "Country of Origin": "China", "Are batteries required?": "No", "Dangerous Goods Regulations": "Not Applicable"}, "child": {"record_action": "Partial Update", "Product Type": "HOME", "Brand Name": "GENERIC", "Parentage Level": "Child", "Child Relationship Type": "Variation", "Variation Theme Name": "COLOR", "Country of Origin": "China", "Are batteries required?": "No", "Dangerous Goods Regulations": "Not Applicable"}}}, "fba": {"name": "FBA商城", "description": "FBA商城字段映射配置 - 基于DECORATIVE_RIBBON_TRIM_US.xlsm模板", "template_info": {"file": "DECORATIVE_RIBBON_TRIM_US.xlsm", "header_row": 2, "field_code_row": 3, "data_start_row": 5, "total_columns": 211}, "field_mappings": {"partial_update": {"Seller SKU": {"parent": "generated_smart", "child": "target_listing.MSKU", "column": 2, "field_code": "item_sku", "description": "卖家SKU - 父体根据实际组合情况智能生成，子体直接映射MSKU"}, "Brand": {"value": "from_data_brand", "column": 3, "field_code": "brand_name", "description": "品牌 - 从目标表格的亚马逊品牌列获取"}, "Update Delete": {"parent": "Update", "child": "PartialUpdate", "column": 4, "field_code": "update_delete", "description": "更新删除 - 父体Update，子体PartialUpdate"}, "Product ID": {"parent": "", "child": "target_listing.ASIN", "column": 5, "field_code": "external_product_id", "description": "产品ID - 父体不填充，子体映射ASIN"}, "Product ID Type": {"parent": "", "child": "ASIN", "column": 6, "field_code": "external_product_id_type", "description": "产品ID类型 - 父体不填充，子体固定值ASIN"}, "Product Type": {"value": "decorativeribbontrim", "column": 1, "field_code": "feed_product_type", "description": "产品类型 - 固定值decorativeribbontrim"}, "Title": {"parent": "generated_smart_title", "child": "", "column": 7, "field_code": "item_name", "description": "商品标题 - 父体根据实际组合情况智能生成，子体不填充"}, "Product Description": {"parent": "generated_same_as_title", "child": "", "column": 10, "field_code": "product_description", "description": "产品描述 - 父体等于Title，子体不填充"}, "Your Price": {"source": "target_listing.价格", "column": 14, "field_code": "standard_price", "description": "价格 - 映射价格字段"}, "Quantity": {"source": "target_listing.quantity", "column": 15, "field_code": "quantity", "description": "数量 - 映射数量字段"}, "Parentage": {"parent": "Parent", "child": "Child", "column": 26, "field_code": "parent_child", "description": "父子关系 - 父体Parent，子体Child"}, "Parent SKU": {"value": "generated_parent_sku", "column": 27, "field_code": "parent_sku", "description": "父体SKU - 引用生成的父体SKU"}, "Relationship Type": {"value": "Variation", "column": 28, "field_code": "relationship_type", "description": "关系类型 - 固定值Variation"}, "Variation Theme": {"value": "COLOR", "column": 29, "field_code": "variation_theme", "description": "变体主题 - 固定值COLOR"}, "Color": {"parent": "", "child": "smart_parse_color", "column": 39, "field_code": "color_name", "description": "颜色 - 父体不填充，子体智能解析"}, "Size": {"parent": "", "child": "smart_parse_size_with_symbol", "column": 44, "field_code": "size_name", "description": "尺寸 - 父体不填充，子体智能解析并使用符号显示"}, "Is this product a battery or does it utilize batteries?": {"value": "No", "column": 107, "field_code": "batteries_required", "description": "是否需要电池 - 固定值No"}, "Country/Region of Origin": {"value": "China", "column": 134, "field_code": "country_of_origin", "description": "原产国 - 固定值China"}, "Category (item-type)": {"value": "fabric-ribbons", "column": 135, "field_code": "item_type", "description": "商品类型 - 固定值fabric-ribbons"}}, "full_update": {"Manufacturer": {"value": "GENERIC", "column": 8, "field_code": "manufacturer", "description": "制造商 - 固定值GENERIC"}, "Main Image URL": {"source": "target_listing.image_url", "column": 16, "field_code": "main_image_url", "description": "主图URL - 映射图片URL字段"}}}, "default_values": {"parent": {"Product Type": "decorativeribbontrim", "Brand": "from_data_brand", "Update Delete": "Update", "Product ID": "", "Product ID Type": "", "Title": "generated_smart_title", "Product Description": "generated_same_as_title", "Parentage": "Parent", "Relationship Type": "Variation", "Variation Theme": "COLOR", "Color": "", "Size": "", "Category (item-type)": "fabric-ribbons", "Country/Region of Origin": "China", "Is this product a battery or does it utilize batteries?": "No"}, "child": {"Product Type": "decorativeribbontrim", "Brand": "from_data_brand", "Update Delete": "PartialUpdate", "Product ID": "from_data", "Product ID Type": "ASIN", "Title": "", "Product Description": "", "Parentage": "Child", "Parent SKU": "generated_parent_sku", "Relationship Type": "Variation", "Variation Theme": "COLOR", "Color": "smart_parse_color", "Size": "smart_parse_size_with_symbol", "Category (item-type)": "fabric-ribbons", "Country/Region of Origin": "China", "Is this product a battery or does it utilize batteries?": "No"}}}}, "data_sources": {"target_listing": {"file": "已上架低价商城listing.xlsx", "sheet": "Sheet1", "key_fields": ["MSKU", "color", "size", "品名", "价格", "SKU", "亚马逊品牌", "ASIN", "变体属性"]}, "product_data": {"file": "产品资料库-丝带-20250416.xlsx", "sheet": "单个产品", "key_fields": ["*SKU（必填）", "SKC", "颜色(主)（必填）", "宽度（分）（必填）", "长度（码）（必填）"]}}, "sku_generation_rules": {"low_price": {"parent_sku_format": "{SKC}", "prefix": "", "suffix": ""}, "fba": {"parent_sku_format": "FBA-{SKC}", "prefix": "FBA-", "suffix": ""}}, "item_name_generation_rules": {"low_price": {"format": "{size} {keyword}", "prefix": ""}, "fba": {"format": "FBA {size} {keyword}", "prefix": "FBA"}}}