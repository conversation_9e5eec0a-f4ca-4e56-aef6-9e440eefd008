"""
测试多商城架构
验证新的商城类型管理和配置系统
"""

import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, project_root)

from marketplace.factory import MarketplaceFactory
from config.config_manager import ConfigManager
from config.settings import Settings


def test_marketplace_factory():
    """测试商城工厂"""
    print("🧪 测试商城工厂...")
    
    # 获取可用商城
    marketplaces = MarketplaceFactory.get_available_marketplaces()
    print(f"📋 可用商城: {marketplaces}")
    
    # 测试创建低价商城
    low_price = MarketplaceFactory.create_marketplace('low_price')
    print(f"🏪 低价商城: {low_price.config.display_name}")
    print(f"   - 模板文件: {low_price.config.template_file}")
    print(f"   - 支持维度: {low_price.config.supported_dimensions}")
    print(f"   - 更新模式: {low_price.config.update_modes}")
    
    # 测试创建FBA商城
    fba = MarketplaceFactory.create_marketplace('fba')
    print(f"📦 FBA商城: {fba.config.display_name}")
    print(f"   - 模板文件: {fba.config.template_file}")
    print(f"   - 支持维度: {fba.config.supported_dimensions}")
    print(f"   - 更新模式: {fba.config.update_modes}")
    
    return True


def test_marketplace_methods():
    """测试商城方法"""
    print("\n🧪 测试商城方法...")
    
    # 测试低价商城
    low_price = MarketplaceFactory.create_marketplace('low_price')
    
    # 测试默认值获取
    parent_defaults = low_price.get_default_values('parent', 'partial')
    print(f"🏪 低价商城父体默认值: {len(parent_defaults)}个字段")
    
    child_defaults = low_price.get_default_values('child', 'partial')
    print(f"🏪 低价商城子体默认值: {len(child_defaults)}个字段")
    
    # 测试SKU生成
    group_data = {'SKC': 'TEST-SKC-001', 'children': []}
    parent_sku = low_price.generate_parent_sku(group_data, 'SKC')
    print(f"🏪 低价商城父体SKU: {parent_sku}")
    
    # 测试商品名称生成
    item_name = low_price.generate_item_name('3/8"-100 Yards', 'satin ribbon')
    print(f"🏪 低价商城商品名称: {item_name}")
    
    # 测试FBA商城
    fba = MarketplaceFactory.create_marketplace('fba')
    
    # 测试SKU生成
    fba_parent_sku = fba.generate_parent_sku(group_data, 'SKC')
    print(f"📦 FBA商城父体SKU: {fba_parent_sku}")
    
    # 测试商品名称生成
    fba_item_name = fba.generate_item_name('3/8"-100 Yards', 'satin ribbon')
    print(f"📦 FBA商城商品名称: {fba_item_name}")
    
    return True


def test_config_manager():
    """测试配置管理器"""
    print("\n🧪 测试配置管理器...")
    
    config_manager = ConfigManager()
    
    # 测试设置和获取配置
    config_manager.set_user_config("test.value", "hello world")
    value = config_manager.get_user_config("test.value")
    print(f"⚙️ 配置测试: {value}")
    
    # 测试商城配置获取
    low_price_config = config_manager.get_marketplace_config('low_price')
    print(f"🏪 低价商城配置: {low_price_config['display_name']}")
    
    fba_config = config_manager.get_marketplace_config('fba')
    print(f"📦 FBA商城配置: {fba_config['display_name']}")
    
    return True


def test_settings():
    """测试设置"""
    print("\n🧪 测试设置...")
    
    settings = Settings()
    print(f"📱 应用名称: {settings.APP_NAME}")
    print(f"📱 应用版本: {settings.APP_VERSION}")
    print(f"📁 项目根目录: {settings.PROJECT_ROOT}")
    print(f"📁 数据目录: {settings.DATA_DIR}")
    
    # 确保目录存在
    settings.ensure_directories()
    print("✅ 目录检查完成")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试多商城架构...")
    print("=" * 60)
    
    try:
        # 运行所有测试
        tests = [
            test_settings,
            test_marketplace_factory,
            test_marketplace_methods,
            test_config_manager
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                    print("✅ 测试通过")
                else:
                    print("❌ 测试失败")
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        print("=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！多商城架构工作正常。")
        else:
            print("⚠️ 部分测试失败，请检查配置。")
            
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")


if __name__ == "__main__":
    main()
