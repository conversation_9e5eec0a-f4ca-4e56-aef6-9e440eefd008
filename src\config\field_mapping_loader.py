"""
字段映射配置加载器
从JSON配置文件中加载字段映射关系和默认值配置
"""

import json
import os
from typing import Dict, Any, Optional
from loguru import logger


class FieldMappingLoader:
    """字段映射配置加载器"""
    
    def __init__(self, config_file_path: Optional[str] = None):
        """
        初始化配置加载器
        
        Args:
            config_file_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_file_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(__file__)
            project_root = os.path.dirname(os.path.dirname(current_dir))
            config_file_path = os.path.join(project_root, 'data', 'config', 'field_mappings.json')
        
        self.config_file_path = config_file_path
        self._config_cache = None
        
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if self._config_cache is not None:
            return self._config_cache
            
        try:
            if not os.path.exists(self.config_file_path):
                logger.warning(f"配置文件不存在: {self.config_file_path}")
                return {}
                
            with open(self.config_file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            self._config_cache = config
            logger.info(f"成功加载字段映射配置: {self.config_file_path}")
            return config
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def get_marketplace_config(self, marketplace_type: str) -> Dict[str, Any]:
        """
        获取指定商城的配置
        
        Args:
            marketplace_type: 商城类型 ('low_price' 或 'fba')
            
        Returns:
            商城配置字典
        """
        config = self._load_config()
        marketplaces = config.get('marketplaces', {})
        return marketplaces.get(marketplace_type, {})
    
    def get_field_mappings(self, marketplace_type: str, update_mode: str = 'partial_update') -> Dict[str, Any]:
        """
        获取字段映射关系
        
        Args:
            marketplace_type: 商城类型
            update_mode: 更新模式 ('partial_update' 或 'full_update')
            
        Returns:
            字段映射字典
        """
        marketplace_config = self.get_marketplace_config(marketplace_type)
        field_mappings = marketplace_config.get('field_mappings', {})
        return field_mappings.get(update_mode, {})
    
    def get_default_values(self, marketplace_type: str, record_type: str) -> Dict[str, Any]:
        """
        获取默认值配置
        
        Args:
            marketplace_type: 商城类型
            record_type: 记录类型 ('parent' 或 'child')
            
        Returns:
            默认值字典
        """
        marketplace_config = self.get_marketplace_config(marketplace_type)
        default_values = marketplace_config.get('default_values', {})
        return default_values.get(record_type, {})
    
    def get_sku_generation_rules(self, marketplace_type: str) -> Dict[str, Any]:
        """
        获取SKU生成规则
        
        Args:
            marketplace_type: 商城类型
            
        Returns:
            SKU生成规则字典
        """
        config = self._load_config()
        sku_rules = config.get('sku_generation_rules', {})
        return sku_rules.get(marketplace_type, {})
    
    def get_item_name_generation_rules(self, marketplace_type: str) -> Dict[str, Any]:
        """
        获取商品名称生成规则
        
        Args:
            marketplace_type: 商城类型
            
        Returns:
            商品名称生成规则字典
        """
        config = self._load_config()
        item_name_rules = config.get('item_name_generation_rules', {})
        return item_name_rules.get(marketplace_type, {})
    
    def get_data_sources_config(self) -> Dict[str, Any]:
        """
        获取数据源配置
        
        Returns:
            数据源配置字典
        """
        config = self._load_config()
        return config.get('data_sources', {})
    
    def get_template_info(self, marketplace_type: str) -> Dict[str, Any]:
        """
        获取模板信息
        
        Args:
            marketplace_type: 商城类型
            
        Returns:
            模板信息字典
        """
        marketplace_config = self.get_marketplace_config(marketplace_type)
        return marketplace_config.get('template_info', {})
    
    def reload_config(self):
        """重新加载配置文件"""
        self._config_cache = None
        logger.info("配置缓存已清除，下次访问时将重新加载")
    
    def validate_config(self) -> tuple[bool, list]:
        """
        验证配置文件的完整性
        
        Returns:
            (是否验证通过, 错误信息列表)
        """
        config = self._load_config()
        errors = []
        
        if not config:
            errors.append("配置文件为空或加载失败")
            return False, errors
        
        # 检查必要的顶级键
        required_keys = ['marketplaces', 'data_sources', 'sku_generation_rules', 'item_name_generation_rules']
        for key in required_keys:
            if key not in config:
                errors.append(f"缺少必要的配置键: {key}")
        
        # 检查商城配置
        marketplaces = config.get('marketplaces', {})
        for marketplace_type in ['low_price', 'fba']:
            if marketplace_type not in marketplaces:
                errors.append(f"缺少商城配置: {marketplace_type}")
                continue
                
            marketplace_config = marketplaces[marketplace_type]
            required_marketplace_keys = ['field_mappings', 'default_values']
            for key in required_marketplace_keys:
                if key not in marketplace_config:
                    errors.append(f"商城 {marketplace_type} 缺少配置键: {key}")
        
        return len(errors) == 0, errors
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Returns:
            配置摘要字典
        """
        config = self._load_config()
        
        summary = {
            'version': config.get('version', 'unknown'),
            'last_updated': config.get('last_updated', 'unknown'),
            'marketplaces': [],
            'total_field_mappings': 0,
            'config_file_path': self.config_file_path
        }
        
        marketplaces = config.get('marketplaces', {})
        for marketplace_type, marketplace_config in marketplaces.items():
            marketplace_info = {
                'type': marketplace_type,
                'name': marketplace_config.get('name', marketplace_type),
                'description': marketplace_config.get('description', ''),
                'field_mappings_count': 0
            }
            
            field_mappings = marketplace_config.get('field_mappings', {})
            for update_mode, mappings in field_mappings.items():
                marketplace_info['field_mappings_count'] += len(mappings)
                summary['total_field_mappings'] += len(mappings)
            
            summary['marketplaces'].append(marketplace_info)
        
        return summary


# 全局配置加载器实例
_field_mapping_loader = None

def get_field_mapping_loader() -> FieldMappingLoader:
    """
    获取全局字段映射配置加载器实例
    使用JSON配置文件进行字段映射

    Returns:
        字段映射配置加载器实例
    """
    global _field_mapping_loader
    if _field_mapping_loader is None:
        # 直接使用JSON配置加载器
        _field_mapping_loader = FieldMappingLoader()
        logger.info("🔧 使用JSON配置文件进行字段映射")

    return _field_mapping_loader


