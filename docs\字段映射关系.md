# 字段映射关系标准

## 📋 项目概述
亚马逊listing拆分合并工具的字段映射关系说明文档，基于需求文档和数据分析结果整理。

## 📁 文件结构分析

### 1. 目标listing表格（已上架低价商城listing.xlsx）
- **工作表**: sheet1
- **表头位置**: 第1行
- **主要字段**: MSKU, FNSKU, SKU, ASIN, 状态, Product Description, color, size等

### 2. 产品资料表格（产品资料库-丝带-20250416.xlsx）
- **工作表**: "单个产品"
- **表头位置**: 第1行
- **主要字段**: *SKU（必填）, 图片, 创建时间, 品名（旧）, SPU款名（内部）等

### 3. 亚马逊模板（低价商城template.xlsm）
- **工作表**: "Template"
- **表头位置**: 第4行
- **总字段数**: 288列
- **填充起始行**: 第7行（父体），第8行开始（子体）

## 🔗 核心映射关系

### 基础映射
1. `目标listing表格.MSKU` → `模板.Seller SKU`（子体）
2. `目标listing表格.Product Description` → `模板.Product Description`
3. `目标listing表格.color` → `模板.Color`

### SKC维度映射
- **父体Seller SKU**: 通过`目标listing表格.SKU`匹配`产品资料.SKC`生成
- **逻辑**: SKC相同的Seller SKU为一个父体

## 📝 默认值配置

### 部分更新模式

#### 父体字段（第7行开始，每个SKC一条记录）
| 字段名 | 默认值 | 生成逻辑 |
|--------|--------|----------|
| Seller SKU | 动态生成 | 基于SKC维度 |
| Record Action | "Full Update" | 固定值 |
| Product Type | "HOME" | 固定值 |
| Item Name | 动态生成 | `目标listing表格.size + 商品关键词` |
| Brand Name | "GENERIC" | 固定值 |
| Item Type Keyword | 用户输入 | 商品关键词 |
| Product Description | 动态生成 | 等于Item Name |
| Bullet Point | 动态生成 | 等于Item Name |
| Parentage Level | "Parent" | 固定值 |
| Child Relationship Type | "Variation" | 固定值 |
| Variation Theme Name | "COLOR" | 固定值 |
| Country of Origin | "China" | 固定值 |
| Are batteries required? | "No" | 固定值 |
| Dangerous Goods Regulations | "Not Applicable" | 固定值 |

#### 子体字段（第8行开始，每个MSKU一条记录）
| 字段名 | 默认值 | 生成逻辑 |
|--------|--------|----------|
| Seller SKU | 目标listing表格.MSKU | 直接映射 |
| Record Action | "Partial Update" | 固定值 |
| Product Type | "HOME" | 固定值 |
| Brand Name | "GENERIC" | 固定值 |
| Color | 目标listing表格.color | 直接映射 |
| Parentage Level | "Child" | 固定值 |
| Child Relationship Type | "Variation" | 固定值 |
| Parent SKU | 生成的模版.Template.A7 | 引用父体Seller SKU |
| Variation Theme Name | "COLOR" | 固定值 |
| Country of Origin | "China" | 固定值 |
| Are batteries required? | "No" | 固定值 |
| Dangerous Goods Regulations | "Not Applicable" | 固定值 |

## ⚠️ 重要注意事项

1. **精准匹配**: 所有字段之间采用精准匹配，不进行智能匹配
2. **创建方式**: 支持"部分更新"和"完全更新"两种模式
3. **父子关系**: 父体基于SKC维度生成，子体基于MSKU维度生成
4. **商品关键词**: 通过GUI界面手动输入

## 📖 示例

### Item Name生成示例
- 如果 `size = "3/8\"-100 Yards"`，`商品关键词 = "satin ribbon"`
- 则 `Item Name = "3/8\"-100 Yards satin ribbon"` 